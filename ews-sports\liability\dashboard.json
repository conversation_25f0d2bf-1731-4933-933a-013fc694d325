{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 4888, "links": [], "panels": [{"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 62, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "kafka_consumergroup_lag_sum{consumergroup=~\"ExposureConsumer\", topic=~\"sport.liability.exposure\", namespace=~\"$namespace\"}", "interval": "", "legendFormat": "{{consumergroup}}", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "kafka_consumergroup_lag_sum{consumergroup=~\"RiskConsumer\", topic=~\"sport.liability.risk\", namespace=~\"$namespace\"}", "hide": false, "interval": "", "legendFormat": "{{consumergroup}}", "refId": "B"}], "title": "Kafka lag", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}, "id": 48, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_num_threads{job=\"$job\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Process Threads", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}, "id": 52, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(process_cpu_seconds_total{job=\"$job\", namespace=\"$namespace\"}[5m])", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Process CPU Seconds", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "µs"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 60, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum((1000 * rate(riskworker_batch_processing_time_sum{job=~\"$job\", namespace=~\"$namespace\"}[10m]))/ \nrate(riskworker_batch_processing_time_count{job=~\"$job\", namespace=~\"$namespace\"}[10m]))", "interval": "", "legendFormat": "Risk", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "sum((1000 * rate(exposureworker_batch_processing_time_sum{job=~\"$job\", namespace=~\"$namespace\"}[10m]))/ \nrate(exposureworker_batch_processing_time_count{job=~\"$job\", namespace=~\"$namespace\"}[10m]))", "hide": false, "interval": "", "legendFormat": "Exposure", "refId": "B"}], "title": "Batch Processing Time", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 8}, "id": 54, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(http_requests_in_progress{job=\"$job\", namespace=\"$namespace\"})", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Requests In Progress", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 8}, "id": 56, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(http_requests_received_total{job=\"$job\", namespace=\"$namespace\"}[30m])", "interval": "", "legendFormat": "{{ action }} {{ code }}", "refId": "A"}], "title": "Requests Received", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 16}, "id": 37, "panels": [], "title": "General", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 11, "x": 0, "y": 17}, "id": 50, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "cache_items_count{namespace=\"$namespace\", job=\"$job\"}", "interval": "", "legendFormat": "{{ cache }}", "refId": "A"}], "title": "<PERSON><PERSON>ems <PERSON>", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 17}, "id": 14, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(execution_failed_total{instance=\"$instance\", uri!~\".*actuator.*\"}[1m])", "interval": "", "legendFormat": "{{action}} [{{code}}] {{controller}}", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "sum(rate(message_publish_errors{namespace=\"$namespace\"}[10m])) by (message_type,period)", "hide": false, "interval": "", "legendFormat": "RabbitMQ {{message_type}} {{exception}}", "refId": "B"}], "title": "Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 17}, "id": 38, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(processing_queue_lag{instance=\"$instance\", namespace=\"$namespace\"}) by (queue)", "interval": "", "legendFormat": "{{queue}} | $job ", "refId": "A"}], "title": "Processing Lags", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 25}, "id": 2, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(container_cpu_usage_seconds_total{pod=\"$podname\"}[5m]))", "interval": "", "legendFormat": "cpu usage", "refId": "A"}], "title": "Number of cores used", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 25}, "id": 24, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_private_memory_bytes{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_private_memory_bytes", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 33}, "id": 22, "panels": [], "title": "Performance", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 34}, "id": 34, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(execution_duration_seconds_sum{instance=\"$instance\", uri!~\".*actuator.*\"}[5m])/rate(execution_duration_seconds_count{instance=\"$instance\", uri!~\".*actuator.*\"}[5m])", "interval": "", "legendFormat": "{{action}} [{{code}}] {{controller}}", "refId": "A"}], "title": "Execution Durations in Seconds", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 34}, "id": 41, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(message_size_bytes_sum{namespace=\"$namespace\"}[10m])) by (message_type) / sum(rate(message_size_bytes_count{namespace=\"$namespace\"}[10m])) by (message_type)", "interval": "", "legendFormat": "{{message_type}}", "refId": "A"}], "title": "RabbitMQ - Bytes (per second)", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 43}, "id": 35, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(execution_duration_seconds_count{job=\"$job\",namespace=\"$namespace\"}[$__rate_interval])) by (controller,action,job)", "interval": "", "legendFormat": "{{job}}.{{controller}}.{{action}}", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "sum(rate(execution_duration_seconds_count{job=\"$job\",namespace=\"$namespace\"}[$__rate_interval])) by (controller,action,job)", "hide": false, "interval": "", "legendFormat": "{{action}} [{{code}}] {{controller}}", "refId": "B"}], "title": "Execution Rate", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 43}, "id": 42, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(message_size_bytes_count{namespace=\"$namespace\"}[10m])) by (message_type)", "interval": "", "legendFormat": "{{message_type}}", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "sum(rate(duplicated_messages{namespace=\"$namespace\"}[10m])) by (message_type)", "hide": false, "interval": "", "legendFormat": "{{message_type}} (Deduplicated)", "refId": "B"}], "title": "RabbitMQ - Messages (per second)", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 53}, "id": 44, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "avg(cache_items_count{job=\"ews-liability-api\"}) by (cache)", "interval": "", "legendFormat": "{{cache}}", "refId": "A"}], "title": "<PERSON><PERSON>", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 63}, "id": 40, "panels": [], "title": "Resources", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 64}, "id": 30, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_start_time_seconds{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_start_time_seconds", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 64}, "id": 26, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_open_handles{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_open_handles", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 64}, "id": 28, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_num_threads{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_num_threads", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 64}, "id": 32, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_working_set_bytes{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_working_set_bytes", "type": "timeseries"}], "preload": false, "refresh": "", "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "", "value": ""}, "label": "Feed Type", "name": "feed_type", "options": [{"selected": true, "text": "Premium", "value": ""}, {"selected": false, "text": "Standard", "value": "standard-"}], "query": "Premium :   ,Standard : standard-", "type": "custom"}, {"current": {"text": "ews-liability-service-risks", "value": "ews-liability-service-risks"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{}, job)", "includeAll": false, "label": "Job", "name": "job", "options": [], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/^${feed_type}ews-liability.*/", "type": "query"}, {"current": {"text": "ews-int", "value": "ews-int"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "includeAll": false, "label": "Namespace", "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "172.17.57.236:80", "value": "172.17.57.236:80"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "includeAll": false, "label": "Instance", "name": "instance", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"current": {"text": "ews-liability-service-risks-dbb7b9c55-mlbnz", "value": "ews-liability-service-risks-dbb7b9c55-mlbnz"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "hide": 2, "includeAll": false, "label": "Pod", "name": "pod", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "ews-liability-service-risks-dbb7b9c55-mlbnz", "value": "ews-liability-service-risks-dbb7b9c55-mlbnz"}, "datasource": {"type": "prometheus"}, "definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "includeAll": false, "label": "Podname", "name": "podname", "options": [], "query": {"query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-12h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Liability DEV", "uid": "c48syA7z2bbb", "version": 1, "weekStart": ""}