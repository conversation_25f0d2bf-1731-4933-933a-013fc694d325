{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 2222, "links": [], "panels": [{"datasource": {"type": "grafana-postgresql-datasource", "uid": "-DrepP1nk"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "grafana-postgresql-datasource", "uid": "-DrepP1nk"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  date AS \"time\",\n  delay\nFROM sport_offering_timing\nWHERE\n  $__timeFilter(date)\nORDER BY 1", "refId": "A", "select": [[{"params": ["delay"], "type": "column"}]], "table": "sport_offering_timing", "timeColumn": "date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Feed delay", "type": "timeseries"}, {"datasource": {"type": "grafana-postgresql-datasource", "uid": "-DrepP1nk"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line+area"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "red", "value": 2000}]}, "unit": "ms"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "1"}, "properties": [{"id": "displayName", "value": "1 - <PERSON> Odds"}]}, {"matcher": {"id": "byRegexp", "options": "3"}, "properties": [{"id": "displayName", "value": "3 - Betradar Ctrl"}]}, {"matcher": {"id": "byRegexp", "options": "6"}, "properties": [{"id": "displayName", "value": "6 - Virtual football"}]}, {"matcher": {"id": "byRegexp", "options": "8"}, "properties": [{"id": "displayName", "value": "8 - Virtual Basketball League"}]}, {"matcher": {"id": "byRegexp", "options": "10"}, "properties": [{"id": "displayName", "value": "10 - Virtual Dog Racing"}]}, {"matcher": {"id": "byRegexp", "options": "11"}, "properties": [{"id": "displayName", "value": "11 - Virtual Horse Classics"}]}, {"matcher": {"id": "byRegexp", "options": "12"}, "properties": [{"id": "displayName", "value": "12 - Virtual Tennis In-Play"}]}, {"matcher": {"id": "byRegexp", "options": "15"}, "properties": [{"id": "displayName", "value": "15 - Virtual Baseball In-Play"}]}, {"matcher": {"id": "byRegexp", "options": "20"}, "properties": [{"id": "displayName", "value": "20 - <PERSON><PERSON><PERSON>"}]}, {"matcher": {"id": "byRegexp", "options": "21"}, "properties": [{"id": "displayName", "value": "21 - <PERSON><PERSON><PERSON>"}]}, {"matcher": {"id": "byRegexp", "options": "30"}, "properties": [{"id": "displayName", "value": "30 - VirtualSports"}]}]}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 0}, "id": 3, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "grafana-postgresql-datasource", "uid": "-DrepP1nk"}, "editorMode": "code", "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  date AS \"time\",\n  delay,\n  producer_id as metric\nFROM sport_offering_timing\nWHERE\n  $__timeFilter(date)\nORDER BY 1 ASC", "refId": "A", "select": [[{"params": ["delay"], "type": "column"}]], "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}, "table": "sport_offering_timing", "timeColumn": "date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Feed delay by producer id", "type": "timeseries"}, {"datasource": {"type": "grafana-postgresql-datasource", "uid": "-DrepP1nk"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "1"}, "properties": [{"id": "displayName", "value": "1 - <PERSON> Odds"}]}, {"matcher": {"id": "byRegexp", "options": "3"}, "properties": [{"id": "displayName", "value": "3 - Betradar Ctrl"}]}, {"matcher": {"id": "byRegexp", "options": "6"}, "properties": [{"id": "displayName", "value": "6 - Virtual football"}]}, {"matcher": {"id": "byRegexp", "options": "8"}, "properties": [{"id": "displayName", "value": "8 - Virtual Basketball League"}]}, {"matcher": {"id": "byRegexp", "options": "10"}, "properties": [{"id": "displayName", "value": "10 - Virtual Dog Racing"}]}, {"matcher": {"id": "byRegexp", "options": "11"}, "properties": [{"id": "displayName", "value": "11 - Virtual Horse Classics"}]}, {"matcher": {"id": "byRegexp", "options": "12"}, "properties": [{"id": "displayName", "value": "12 - Virtual Tennis In-Play"}]}, {"matcher": {"id": "byRegexp", "options": "15"}, "properties": [{"id": "displayName", "value": "15 - Virtual Baseball In-Play"}]}, {"matcher": {"id": "byRegexp", "options": "20"}, "properties": [{"id": "displayName", "value": "20 - <PERSON><PERSON><PERSON>"}]}, {"matcher": {"id": "byRegexp", "options": "21"}, "properties": [{"id": "displayName", "value": "21 - <PERSON><PERSON><PERSON>"}]}, {"matcher": {"id": "byRegexp", "options": "30"}, "properties": [{"id": "displayName", "value": "30 - VirtualSports"}]}]}, "gridPos": {"h": 9, "w": 8, "x": 0, "y": 9}, "id": 6, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "grafana-postgresql-datasource", "uid": "-DrepP1nk"}, "editorMode": "code", "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  date AS \"time\",\n  time_in_trading,\n  producer_id as metric\nFROM sport_offering_timing\nWHERE\n  $__timeFilter(date)\nORDER BY time ASC", "refId": "A", "select": [[{"params": ["delay"], "type": "column"}]], "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}, "table": "sport_offering_timing", "timeColumn": "date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Feed delay time in trading", "type": "timeseries"}, {"datasource": {"type": "grafana-postgresql-datasource", "uid": "-DrepP1nk"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "1"}, "properties": [{"id": "displayName", "value": "1 - <PERSON> Odds"}]}, {"matcher": {"id": "byRegexp", "options": "3"}, "properties": [{"id": "displayName", "value": "3 - Betradar Ctrl"}]}, {"matcher": {"id": "byRegexp", "options": "6"}, "properties": [{"id": "displayName", "value": "6 - Virtual football"}]}, {"matcher": {"id": "byRegexp", "options": "8"}, "properties": [{"id": "displayName", "value": "8 - Virtual Basketball League"}]}, {"matcher": {"id": "byRegexp", "options": "10"}, "properties": [{"id": "displayName", "value": "10 - Virtual Dog Racing"}]}, {"matcher": {"id": "byRegexp", "options": "11"}, "properties": [{"id": "displayName", "value": "11 - Virtual Horse Classics"}]}, {"matcher": {"id": "byRegexp", "options": "12"}, "properties": [{"id": "displayName", "value": "12 - Virtual Tennis In-Play"}]}, {"matcher": {"id": "byRegexp", "options": "15"}, "properties": [{"id": "displayName", "value": "15 - Virtual Baseball In-Play"}]}, {"matcher": {"id": "byRegexp", "options": "20"}, "properties": [{"id": "displayName", "value": "20 - <PERSON><PERSON><PERSON>"}]}, {"matcher": {"id": "byRegexp", "options": "21"}, "properties": [{"id": "displayName", "value": "21 - <PERSON><PERSON><PERSON>"}]}, {"matcher": {"id": "byRegexp", "options": "30"}, "properties": [{"id": "displayName", "value": "30 - VirtualSports"}]}]}, "gridPos": {"h": 9, "w": 8, "x": 8, "y": 9}, "id": 4, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "grafana-postgresql-datasource", "uid": "-DrepP1nk"}, "editorMode": "code", "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  date AS \"time\",\n  redis_wait,\n  producer_id as metric\nFROM sport_offering_timing\nWHERE\n  $__timeFilter(date)\nORDER BY time ASC", "refId": "A", "select": [[{"params": ["delay"], "type": "column"}]], "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}, "table": "sport_offering_timing", "timeColumn": "date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Feed delay redis wait", "type": "timeseries"}, {"datasource": {"type": "grafana-postgresql-datasource", "uid": "-DrepP1nk"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "1"}, "properties": [{"id": "displayName", "value": "1 - <PERSON> Odds"}]}, {"matcher": {"id": "byRegexp", "options": "3"}, "properties": [{"id": "displayName", "value": "3 - Betradar Ctrl"}]}, {"matcher": {"id": "byRegexp", "options": "6"}, "properties": [{"id": "displayName", "value": "6 - Virtual football"}]}, {"matcher": {"id": "byRegexp", "options": "8"}, "properties": [{"id": "displayName", "value": "8 - Virtual Basketball League"}]}, {"matcher": {"id": "byRegexp", "options": "10"}, "properties": [{"id": "displayName", "value": "10 - Virtual Dog Racing"}]}, {"matcher": {"id": "byRegexp", "options": "11"}, "properties": [{"id": "displayName", "value": "11 - Virtual Horse Classics"}]}, {"matcher": {"id": "byRegexp", "options": "12"}, "properties": [{"id": "displayName", "value": "12 - Virtual Tennis In-Play"}]}, {"matcher": {"id": "byRegexp", "options": "15"}, "properties": [{"id": "displayName", "value": "15 - Virtual Baseball In-Play"}]}, {"matcher": {"id": "byRegexp", "options": "20"}, "properties": [{"id": "displayName", "value": "20 - <PERSON><PERSON><PERSON>"}]}, {"matcher": {"id": "byRegexp", "options": "21"}, "properties": [{"id": "displayName", "value": "21 - <PERSON><PERSON><PERSON>"}]}, {"matcher": {"id": "byRegexp", "options": "30"}, "properties": [{"id": "displayName", "value": "30 - VirtualSports"}]}]}, "gridPos": {"h": 9, "w": 8, "x": 16, "y": 9}, "id": 5, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "grafana-postgresql-datasource", "uid": "-DrepP1nk"}, "editorMode": "code", "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  date AS \"time\",\n  time_in_offering,\n  producer_id as metric\nFROM sport_offering_timing\nWHERE\n  $__timeFilter(date)\nORDER BY time ASC", "refId": "A", "select": [[{"params": ["delay"], "type": "column"}]], "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}, "table": "sport_offering_timing", "timeColumn": "date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Feed delay time in offering", "type": "timeseries"}], "preload": false, "refresh": "", "schemaVersion": 40, "tags": [], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Sport-Offering-Delay-UAT", "uid": "qEKXxwqGk222", "version": 1, "weekStart": ""}