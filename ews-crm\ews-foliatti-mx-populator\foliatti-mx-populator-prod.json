{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 723, "iteration": 1722236224189, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 22, "panels": [], "title": "Processed messages by topics", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "All the messages that passed validation and were suitable for processing for topic payment-transfers . The graphic  sums them by by different state whether they were processed, mapped or produced successfully. The sum is by state and provides info about the flow", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 1}, "hiddenSeries": false, "id": 27, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (topic,state) (increase(processed_messages_count{job=\"ews-foliatti-mx-populator\", namespace=\"$namespace\", topic=\"payment-transfers\"}[5m]))", "hide": false, "interval": "", "legendFormat": "{{state}} ", "refId": "B"}, {"expr": "sum by (topic) (increase(produced_messages_count{job=\"ews-foliatti-mx-populator\", namespace=\"$namespace\", topic=~\"payment-transfers-ftmx|payment-transfers-dev-ftmx|payment-transfers-int-ftmx\"}[5m]))", "hide": false, "interval": "", "legendFormat": "produced", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Payment-transfers", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:585", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:586", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "All the messages that passed validation and were suitable for processing for topic game-session-versions/casino-bet. The graphic  sums them by by different state whether they were processed, mapped or produced successfully. The sum is by state and provides info about the flow", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 1}, "hiddenSeries": false, "id": 26, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (topic,state) (increase(processed_messages_count{job=\"ews-foliatti-mx-populator\", namespace=\"$namespace\", topic=\"game-session-versions\"}[5m]))", "hide": false, "interval": "", "legendFormat": "{{state}} ", "refId": "B"}, {"expr": "sum by (topic) (increase(produced_messages_count{job=\"ews-foliatti-mx-populator\", namespace=\"$namespace\", topic=~\"casino-bets-ftmx|casino-bets-dev-ftmx|casino-bets-int-ftmx\"}[5m]))", "hide": false, "interval": "", "legendFormat": "produced", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Game-session-versions/Casino-bets", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:585", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:586", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "All the messages that passed validation and were suitable for processing for topic player-data/player-beneficiary . The graphic  sums them by by different state whether they were processed, mapped or produced successfully. The sum is by state and provides info about the flow", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 7}, "hiddenSeries": false, "id": 24, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (state) (increase(processed_messages_count{job=\"ews-foliatti-mx-populator\", namespace=\"$namespace\", topic=\"player-data\"}[5m]))", "hide": false, "interval": "", "legendFormat": "{{state}} ", "refId": "B"}, {"expr": "sum by () (increase(produced_messages_count{job=\"ews-foliatti-mx-populator\", namespace=\"$namespace\", topic=~\"player-data-ftmx|player-data-dev-ftmx|player-data-int-ftmx|player-beneficiary-ftmx|player-beneficiary-dev-ftmx|player-beneficiary-int|ftmx|player-login-ftmx|player-login-dev-ftmx|player-login-int|ftmx\"}[5m]))", "hide": false, "interval": "", "legendFormat": "produced", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Player-data/Player-beneficiary/Player-login", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:585", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:586", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "All the messages that passed validation and were suitable for processing for topic sport-transactions/sport-bets  . The graphic  sums them by by different state whether they were processed, mapped or produced successfully. The sum is by state and provides info about the flow", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 7}, "hiddenSeries": false, "id": 25, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (topic,state) (increase(processed_messages_count{job=\"ews-foliatti-mx-populator\", namespace=\"$namespace\", topic=\"sport-transactions\"}[5m]))", "hide": false, "interval": "", "legendFormat": "{{state}} ", "refId": "B"}, {"expr": "sum by (topic) (increase(produced_messages_count{job=\"ews-foliatti-mx-populator\", namespace=\"$namespace\", topic=~\"sport-bets-ftmx|sport-bets-dev-ftmx|sport-bets-int-ftmx\"}[5m]))", "hide": false, "interval": "", "legendFormat": "produced ", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Sport-transactions/Sport-bets", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:585", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:586", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 13}, "id": 29, "panels": [], "title": "Errors", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "This tracks total exceptions thrown  in the app regardless from where or what. General error counter.\nAlso errors happened when producing messages to kafka separated by topic", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 14}, "hiddenSeries": false, "id": 14, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (topic) (increase(failed_messages_count{job=\"ews-foliatti-mx-populator\", namespace=\"$namespace\"}[5m]))", "interval": "", "legendFormat": "Failed to produce - {{topic}}", "refId": "A"}, {"expr": "sum by () (increase(error_count{job=\"ews-foliatti-mx-populator\", namespace=\"$namespace\"}[5m]))", "hide": false, "interval": "", "legendFormat": "Errors count", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Errors count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:755", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:756", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "ews-foliatti-mx-populator", "value": "ews-foliatti-mx-populator"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{}, job)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Job", "multi": false, "name": "job", "options": [], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/ews-foliatti-mx-populator.*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-sof-prod", "value": "ews-sof-prod"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "************:8080", "value": "************:8080"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Instance", "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-foliatti-mx-populator-7bfb8ddcf8-wq4m9", "value": "ews-foliatti-mx-populator-7bfb8ddcf8-wq4m9"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "description": null, "error": null, "hide": 2, "includeAll": false, "label": "Pod", "multi": false, "name": "pod", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-foliatti-mx-populator-7bfb8ddcf8-wq4m9", "value": "ews-foliatti-mx-populator-7bfb8ddcf8-wq4m9"}, "datasource": null, "definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Podname", "multi": false, "name": "podname", "options": [], "query": {"query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "All", "value": "$__all"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Topic", "multi": false, "name": "topic", "options": [], "query": "", "skipUrlSync": false, "type": "custom"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "foliatti-mx-populator", "uid": "b300950469d44282b2a9", "version": 3}