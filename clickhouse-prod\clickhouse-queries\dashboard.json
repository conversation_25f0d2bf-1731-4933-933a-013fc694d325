{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Helps to visualize most frequent, slowest, failed queries.\r\nShows queries rate per second, table with last queries", "editable": true, "gnetId": 2515, "graphTooltip": 1, "id": 803, "iteration": 1726641244199, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 22, "panels": [], "repeat": null, "title": "Top charts", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Altinity plugin for ClickHouse PROD", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 20, "x": 0, "y": 1}, "hiddenSeries": false, "id": 15, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"database": "system", "dateColDataType": "event_date", "dateLoading": false, "dateTimeColDataType": "event_time", "datetimeLoading": false, "expr": "", "formattedQuery": "<font color=\"darkcyan\">$rateColumns</font>(<br />Â Â Â Â <font color=\"navajowhite\">substring</font>(query,Â Â <font color=\"cornflowerblue\">1</font>,Â Â <font color=\"cornflowerblue\">45</font>) <font color=\"darkorange\">AS</font> query,<br />Â Â Â Â <font color=\"navajowhite\">count</font>() c)<br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font><br />Â Â Â Â <font color=\"navajowhite\">cityHash64</font>(query) <font color=\"darkorange\">global</font> <font color=\"darkorange\">in</font> (<br />Â Â Â Â <font color=\"darkorange\">SELECT</font> <font color=\"navajowhite\">cityHash64</font>(<font color=\"navajowhite\">substring</font>(query,Â Â <font color=\"cornflowerblue\">1</font>,Â Â <font color=\"cornflowerblue\">45</font>)) <font color=\"darkorange\">AS</font> h<br />Â Â Â Â <font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br />Â Â Â Â <font color=\"darkorange\">WHERE</font><br />Â Â Â Â Â Â Â Â <font color=\"darkcyan\">$timeFilter</font><br />Â Â Â Â Â Â Â Â <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />Â Â Â Â Â Â Â Â <font color=\"yellow\">AND</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Â Â Â Â Â Â Â Â <font color=\"yellow\">AND</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Â Â <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))<br />Â Â Â Â <font color=\"darkorange\">GROUP BY</font> h<br />Â Â Â Â <font color=\"darkorange\">ORDER BY</font> <font color=\"navajowhite\">count</font>() <font color=\"darkorange\">desc</font><br />Â Â Â Â <font color=\"darkorange\">LIMIT</font> <font color=\"darkcyan\">$top</font>)<br />Â Â Â Â <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />Â Â Â Â <font color=\"yellow\">AND</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Â Â Â Â <font color=\"yellow\">AND</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Â Â <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))", "intervalFactor": 2, "query": "$rateColumns(\n    substring(query,  1,  45) AS query,\n    count() c)\nFROM $table\nWHERE\n    cityHash64(query) global in (\n    SELECT cityHash64(substring(query,  1,  45)) AS h\n    FROM $table\n    WHERE\n        $timeFilter\n        AND type in ($type)\n        AND initial_user in ($user)\n        AND('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))\n    GROUP BY h\n    ORDER BY count() desc\n    LIMIT $top)\n    AND type in ($type)\n    AND initial_user in ($user)\n    AND('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))", "rawQuery": "SELECT t, arrayMap(a -> (a.1, a.2/runningDifference( t/1000 )), groupArr) FROM (SELECT t, groupArray((query, c)) as groupArr FROM ( SELECT (intDiv(toUInt32(event_time), 2) * 2) * 1000 as t, substring(query,  1,  45) AS query, count() c FROM system.query_log WHERE event_date >= toDate(1498209947) AND event_time >= toDateTime(1498209947) AND      cityHash64(query) global in (     SELECT cityHash64(substring(query,  1,  45)) AS h     FROM system.query_log     WHERE         event_date >= toDate(1498209947) AND event_time >= toDateTime(1498209947)         AND type in (1,2,3,4)         AND initial_user in ('default')         AND('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1))     GROUP BY h     ORDER BY count() desc     LIMIT 5)     AND type in (1,2,3,4)     AND initial_user in ('default')     AND('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1)) GROUP BY t, query  ORDER BY t) GROUP BY t ORDER BY t)", "refId": "A", "resultFormat": "time_series", "table": "query_log", "tableLoading": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Top $top request's rate by type: $type; user: $user; query type: $query_type", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 20, "y": 1}, "id": 17, "links": [], "options": {"content": "1 - successful start of query execution\n\n2 - successful end of query execution\n\n3 - exception before start of query execution\n\n4 - exception while query execution", "mode": "markdown"}, "pluginVersion": "7.4.2", "title": "Types", "transparent": true, "type": "text"}, {"columns": [], "datasource": "Altinity plugin for ClickHouse PROD", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 11, "w": 8, "x": 0, "y": 8}, "height": "400px", "id": 18, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": 2, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "hidden"}, {"alias": "duration", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "pattern": "duration", "thresholds": [], "type": "number", "unit": "ms"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": null, "pattern": "count", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"database": "system", "dateColDataType": "event_date", "dateLoading": false, "dateTimeColDataType": "event_time", "datetimeLoading": false, "expr": "", "format": "table", "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />Â Â Â Â <font color=\"navajowhite\">rand</font>() <font color=\"darkorange\">as</font> t,<br />Â Â Â Â <font color=\"navajowhite\">substring</font>(query,Â Â <font color=\"cornflowerblue\">1</font>,Â Â <font color=\"cornflowerblue\">70</font>) <font color=\"darkorange\">AS</font> query,<br />Â Â Â Â <font color=\"navajowhite\">avg</font>(query_duration_ms) duration,<br />Â Â Â Â <font color=\"navajowhite\">count</font>() <font color=\"navajowhite\">count</font><br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font><br />Â Â Â Â <font color=\"darkcyan\">$timeFilter</font><br />Â Â Â Â <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />Â Â Â Â <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Â Â Â Â <font color=\"yellow\">and</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Â Â <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))<br /><font color=\"darkorange\">GROUP BY</font> query<br /><font color=\"darkorange\">ORDER BY</font> duration <font color=\"darkorange\">desc</font><br /><font color=\"darkorange\">LIMIT</font> <font color=\"darkcyan\">$top</font>", "intervalFactor": 2, "query": "SELECT\n    rand() as t,\n    substring(query,  1,  70) AS query,\n    avg(query_duration_ms) duration,\n    count() count\nFROM $table\nWHERE\n    $timeFilter\n    AND type in ($type)\n    and initial_user in ($user)\n    and('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))\nGROUP BY query\nORDER BY duration desc\nLIMIT $top", "rawQuery": "SELECT     rand() as t,     substring(query,  1,  70) AS query,     avg(query_duration_ms) duration,     count() count FROM system.query_log WHERE     event_date >= toDate(1498209947) AND event_time >= toDateTime(1498209947)     AND type in (1,2,3,4)     and initial_user in ('default')     and('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1)) GROUP BY query ORDER BY duration desc LIMIT 5", "refId": "A", "resultFormat": "time_series", "table": "query_log", "tableLoading": false}], "title": "Top slow queries by type: $type; user: $user; query type: $query_type", "transform": "table", "type": "table-old"}, {"columns": [], "datasource": "Altinity plugin for ClickHouse PROD", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 11, "w": 8, "x": 8, "y": 8}, "height": "400px", "id": 19, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": 2, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "hidden"}, {"alias": "usage", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "pattern": "usage", "thresholds": [], "type": "number", "unit": "bytes"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": null, "pattern": "count", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"database": "system", "dateColDataType": "event_date", "dateLoading": false, "dateTimeColDataType": "event_time", "datetimeLoading": false, "expr": "", "format": "table", "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />Â Â Â Â <font color=\"navajowhite\">rand</font>() <font color=\"darkorange\">as</font> t,<br />Â Â Â Â <font color=\"navajowhite\">substring</font>(query,Â Â <font color=\"cornflowerblue\">1</font>,Â Â <font color=\"cornflowerblue\">70</font>) <font color=\"darkorange\">AS</font> query,<br />Â Â Â Â <font color=\"navajowhite\">avg</font>(memory_usage) usage,<br />Â Â Â Â <font color=\"navajowhite\">count</font>() <font color=\"navajowhite\">count</font><br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font><br />Â Â Â Â <font color=\"darkcyan\">$timeFilter</font><br />Â Â Â Â <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />Â Â Â Â <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Â Â Â Â <font color=\"yellow\">and</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Â Â <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))<br /><font color=\"darkorange\">GROUP BY</font> query<br /><font color=\"darkorange\">ORDER BY</font> usage <font color=\"darkorange\">desc</font><br /><font color=\"darkorange\">LIMIT</font> <font color=\"darkcyan\">$top</font>", "intervalFactor": 2, "query": "SELECT\n    rand() as t,\n    substring(query,  1,  70) AS query,\n    avg(memory_usage) usage,\n    count() count\nFROM $table\nWHERE\n    $timeFilter\n    AND type in ($type)\n    and initial_user in ($user)\n    and('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))\nGROUP BY query\nORDER BY usage desc\nLIMIT $top", "rawQuery": "SELECT     rand() as t,     substring(query,  1,  70) AS query,     avg(memory_usage) usage,     count() count FROM system.query_log WHERE     event_date >= toDate(1498209947) AND event_time >= toDateTime(1498209947)     AND type in (1,2,3,4)     and initial_user in ('default')     and('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1)) GROUP BY query ORDER BY usage desc LIMIT 5", "refId": "A", "resultFormat": "time_series", "table": "query_log", "tableLoading": false}], "title": "Top memory consumers by type: $type; user: $user; query type: $query_type", "transform": "table", "type": "table-old"}, {"columns": [], "datasource": "Altinity plugin for ClickHouse PROD", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 11, "w": 8, "x": 16, "y": 8}, "height": "400px", "id": 20, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": 3, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "hidden"}, {"alias": "type", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": null, "pattern": "type", "thresholds": [], "type": "number", "unit": "none"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": null, "pattern": "count", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"database": "system", "dateColDataType": "event_date", "dateLoading": false, "dateTimeColDataType": "event_time", "datetimeLoading": false, "expr": "", "format": "table", "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />Â Â Â Â <font color=\"navajowhite\">rand</font>() <font color=\"darkorange\">as</font> t,<br />Â Â Â Â <font color=\"navajowhite\">substring</font>(query,Â Â <font color=\"cornflowerblue\">1</font>,Â Â <font color=\"cornflowerblue\">70</font>) <font color=\"darkorange\">AS</font> query,<br />Â Â Â Â <font color=\"darkorange\">type</font>,<br />Â Â Â Â <font color=\"navajowhite\">count</font>() <font color=\"navajowhite\">count</font><br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font><br />Â Â Â Â <font color=\"darkcyan\">$timeFilter</font><br />Â Â Â Â <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"cornflowerblue\">3</font>,<font color=\"cornflowerblue\">4</font>)<br />Â Â Â Â <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Â Â Â Â <font color=\"yellow\">and</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Â Â <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))<br /><font color=\"darkorange\">GROUP BY</font><br />Â Â Â Â query,<br />Â Â Â Â <font color=\"darkorange\">type</font><br /><font color=\"darkorange\">ORDER BY</font> <font color=\"navajowhite\">count</font> <font color=\"darkorange\">desc</font><br /><font color=\"darkorange\">LIMIT</font> <font color=\"darkcyan\">$top</font>", "intervalFactor": 2, "query": "SELECT\n    rand() as t,\n    substring(query,  1,  70) AS query,\n    type,\n    count() count\nFROM $table\nWHERE\n    $timeFilter\n    AND type in (3,4)\n    and initial_user in ($user)\n    and('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))\nGROUP BY\n    query,\n    type\nORDER BY count desc\nLIMIT $top", "rawQuery": "SELECT     rand() as t,     substring(query,  1,  70) AS query,     type,     count() count FROM system.query_log WHERE     event_date >= toDate(1498209947) AND event_time >= toDateTime(1498209947)     AND type in (3,4)     and initial_user in ('default')     and('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1)) GROUP BY     query,     type ORDER BY count desc LIMIT 5", "refId": "A", "resultFormat": "time_series", "table": "query_log", "tableLoading": false}], "title": "Top failed queries by user: $user; query type: $query_type", "transform": "table", "type": "table-old"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 23, "panels": [], "repeat": null, "title": "Request charts", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Altinity plugin for ClickHouse PROD", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 20}, "hiddenSeries": false, "id": 14, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"database": "system", "dateColDataType": "event_date", "dateLoading": false, "dateTimeColDataType": "event_time", "datetimeLoading": false, "expr": "", "formattedQuery": "<font color=\"darkcyan\">$rate</font>(<font color=\"navajowhite\">count</font>() c)<br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">where</font>Â Â <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />Â Â Â Â <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Â Â Â Â <font color=\"yellow\">and</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Â Â <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))", "intervalFactor": 2, "query": "$rate(count() c)\nFROM $table\nwhere  type in ($type)\n    and initial_user in ($user)\n    and('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))", "rawQuery": "SELECT t, c/runningDifference(t/1000) cRate FROM ( SELECT (intDiv(toUInt32(event_time), 2) * 2) * 1000 as t, count() c FROM system.query_log WHERE event_date >= toDate(1498209895) AND event_time >= toDateTime(1498209895) AND   type in (1,2,3,4)     and initial_user in ('default')     and('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1)) GROUP BY t ORDER BY t)", "refId": "A", "resultFormat": "time_series", "table": "query_log", "tableLoading": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Reqs/s by type: $type; user: $user; query type: $query_type", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Altinity plugin for ClickHouse PROD", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 20}, "hiddenSeries": false, "id": 16, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "insert_duration", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"database": "system", "dateColDataType": "event_date", "dateLoading": false, "dateTimeColDataType": "event_time", "datetimeLoading": false, "expr": "", "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />Â Â Â Â <font color=\"darkcyan\">$timeSeries</font> <font color=\"darkorange\">as</font> t,<br />Â Â Â Â <font color=\"navajowhite\">avg</font>(query_duration_ms)Â Â select_duration<br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font><br />Â Â Â Â <font color=\"darkcyan\">$timeFilter</font><br />Â Â Â Â <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">2</font><br />Â Â Â Â <font color=\"yellow\">and</font> positionCaseInsensitive(query,Â Â <font color=\"lightgreen\">'select'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font><br />Â Â Â Â <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br /><font color=\"darkorange\">GROUP BY</font> t<br /><font color=\"darkorange\">ORDER BY</font> t", "intervalFactor": 2, "query": "SELECT\n    $timeSeries as t,\n    avg(query_duration_ms)  select_duration\nFROM $table\nWHERE\n    $timeFilter\n    AND type = 2\n    and positionCaseInsensitive(query,  'select') = 1\n    and initial_user in ($user)\nGROUP BY t\nORDER BY t", "rawQuery": "SELECT     (intDiv(toUInt32(event_time), 2) * 2) * 1000 as t,     avg(query_duration_ms)  select_duration FROM system.query_log WHERE     event_date >= toDate(1498209895) AND event_time >= toDateTime(1498209895)     AND type = 2     and positionCaseInsensitive(query,  'select') = 1     and initial_user in ('default') GROUP BY t ORDER BY t", "refId": "A", "resultFormat": "time_series", "table": "query_log", "tableLoading": false}, {"database": "system", "dateColDataType": "event_date", "dateLoading": false, "dateTimeColDataType": "event_time", "datetimeLoading": false, "expr": "", "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />Â Â Â Â <font color=\"darkcyan\">$timeSeries</font> <font color=\"darkorange\">as</font> t,<br />Â Â Â Â <font color=\"navajowhite\">avg</font>(query_duration_ms) insert_duration<br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font><br />Â Â Â Â <font color=\"darkcyan\">$timeFilter</font><br />Â Â Â Â <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">2</font><br /><font color=\"yellow\">and</font> positionCaseInsensitive(query,Â Â <font color=\"lightgreen\">'insert into'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font><br />Â Â Â Â <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br /><font color=\"darkorange\">GROUP BY</font> t<br /><font color=\"darkorange\">ORDER BY</font> t", "intervalFactor": 2, "query": "SELECT\n    $timeSeries as t,\n    avg(query_duration_ms) insert_duration\nFROM $table\nWHERE\n    $timeFilter\n    AND type = 2\nand positionCaseInsensitive(query,  'insert into') = 1\n    and initial_user in ($user)\nGROUP BY t\nORDER BY t", "rawQuery": "SELECT     (intDiv(toUInt32(event_time), 2) * 2) * 1000 as t,     avg(query_duration_ms) insert_duration FROM system.query_log WHERE     event_date >= toDate(1498209895) AND event_time >= toDateTime(1498209895)     AND type = 2 and positionCaseInsensitive(query,  'insert into') = 1     and initial_user in ('default') GROUP BY t ORDER BY t", "refId": "B", "resultFormat": "time_series", "table": "query_log", "tableLoading": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Query duration by type: $type; user: $user", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 27}, "id": 24, "panels": [], "repeat": null, "title": "Query log table", "type": "row"}, {"columns": [], "datasource": "Altinity plugin for ClickHouse PROD", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 7, "w": 24, "x": 0, "y": 28}, "id": 21, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": null, "desc": false}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": null, "pattern": "duration", "thresholds": [], "type": "number", "unit": "ms"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "pattern": "memory", "thresholds": [], "type": "number", "unit": "bytes"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"database": "system", "dateColDataType": "event_date", "dateLoading": false, "dateTimeColDataType": "event_time", "datetimeLoading": false, "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />Â Â Â Â event_time,<br />Â Â Â Â user,<br />Â Â Â Â query_duration_ms duration,<br />Â Â Â Â memory_usage memory,<br />Â Â Â Â if(exception<font color=\"yellow\">!=</font><font color=\"lightgreen\">''</font>, <font color=\"lightgreen\">'fail'</font>, <font color=\"lightgreen\">'success'</font>) result,<br />Â Â Â Â <font color=\"navajowhite\">concat</font>(<font color=\"navajowhite\">substring</font>(query,<font color=\"cornflowerblue\">1</font>,<font color=\"cornflowerblue\">120</font>), <font color=\"lightgreen\">'...'</font>) query<br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font> <font color=\"darkcyan\">$timeFilter</font><br />Â Â Â Â <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />Â Â Â Â <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Â Â Â Â <font color=\"yellow\">and</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Â Â <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))<br /><font color=\"darkorange\">ORDER BY</font> event_time <font color=\"darkorange\">DESC</font><br /><font color=\"darkorange\">LIMIT</font> <font color=\"cornflowerblue\">1000</font>", "intervalFactor": 1, "query": "SELECT\n    event_time,\n    user,\n    query_duration_ms duration,\n    memory_usage memory,\n    if(exception!='', 'fail', 'success') result,\n    concat(substring(query,1,120), '...') query\nFROM $table\nWHERE $timeFilter\n    AND type in ($type)\n    and initial_user in ($user)\n    and('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))\nORDER BY event_time DESC\nLIMIT 1000", "rawQuery": "SELECT     event_time,     user,     query_duration_ms duration,     memory_usage memory,     if(exception!='', 'fail', 'success') result,     concat(substring(query,1,120), '...') query FROM system.query_log WHERE event_date >= toDate(1498209895) AND event_time >= toDateTime(1498209895)     AND type in (1,2,3,4)     and initial_user in ('default')     and('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1)) ORDER BY event_time DESC LIMIT 1000", "refId": "A", "resultFormat": "time_series", "table": "query_log", "tableLoading": false}], "title": "Query log by type: $type; user: $user; query type: $query_type", "transform": "timeseries_to_columns", "type": "table-old"}], "refresh": "5s", "schemaVersion": 27, "style": "dark", "tags": ["clickhouse", "performance"], "templating": {"list": [{"auto": true, "auto_count": 100, "auto_min": "1m", "current": {"selected": false, "text": "5m", "value": "5m"}, "description": null, "error": null, "hide": 2, "label": null, "name": "interval", "options": [{"selected": false, "text": "auto", "value": "$__auto_interval_interval"}, {"selected": true, "text": "5m", "value": "5m"}], "query": "5m", "refresh": 2, "skipUrlSync": false, "type": "interval"}, {"allValue": null, "current": {"selected": true, "tags": [], "text": ["All"], "value": ["$__all"]}, "description": null, "error": null, "hide": 0, "includeAll": true, "label": "type", "multi": true, "name": "type", "options": [{"selected": true, "text": "All", "value": "$__all"}, {"selected": false, "text": "1", "value": "1"}, {"selected": false, "text": "2", "value": "2"}, {"selected": false, "text": "3", "value": "3"}, {"selected": false, "text": "4", "value": "4"}], "query": "1,2,3,4", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"allValue": null, "current": {"selected": true, "tags": [], "text": "5", "value": "5"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": "top elements", "multi": false, "name": "top", "options": [{"selected": true, "text": "5", "value": "5"}, {"selected": false, "text": "10", "value": "10"}, {"selected": false, "text": "15", "value": "15"}, {"selected": false, "text": "20", "value": "20"}, {"selected": false, "text": "25", "value": "25"}, {"selected": false, "text": "30", "value": "30"}], "query": "5,10,15,20,25,30", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"allValue": "", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "Altinity plugin for ClickHouse PROD", "definition": "", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "initial user", "multi": true, "name": "user", "options": [], "query": "select distinct initial_user from system.query_log where event_date >= today()-3 and event_time > toDateTime(today()-3)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": "", "current": {"selected": true, "text": "all", "value": "all"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": "query type", "multi": false, "name": "query_type", "options": [{"selected": true, "text": "all", "value": "all"}, {"selected": false, "text": "select", "value": "select"}, {"selected": false, "text": "insert", "value": "insert"}], "query": "all,select,insert", "queryValue": "", "skipUrlSync": false, "type": "custom"}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "ClickHouse Queries", "uid": "PIv8roEIk", "version": 4}