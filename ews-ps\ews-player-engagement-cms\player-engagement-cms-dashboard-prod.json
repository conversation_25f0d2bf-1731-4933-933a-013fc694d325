{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 1033, "iteration": 1750257229449, "links": [], "panels": [{"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 37, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "increase(http_requests_received_total{\njob=\"ews-player-engagement-cms\", \nnamespace=\"$namespace\"\n}[$__interval])", "interval": "", "legendFormat": "{{code}} {{method}} {{endpoint}} {{instance}}", "range": true, "refId": "A"}], "title": "Throughput", "transparent": true, "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 36, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": ["max"], "displayMode": "table", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max by (endpoint) (histogram_quantile(1,increase(http_request_duration_seconds_bucket{job=\"ews-player-engagement-cms\",namespace=\"$namespace\"}[$__interval])))", "hide": false, "interval": "", "legendFormat": "{{method}} {{endpoint}}", "range": true, "refId": "A"}], "title": "Latency", "transparent": true, "type": "timeseries"}, {"datasource": null, "description": "Displaying the CPU usage of the ews-player-engagement-cms containers as a percentage of their allocated CPU resources. The query calculates the average CPU usage over the last 5 minutes and compares it to the CPU allocation defined for each container. Values above 100% indicate the container is using more CPU than what has been allocated to it.", "fieldConfig": {"defaults": {"color": {"fixedColor": "semi-dark-green", "mode": "fixed"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "byFrameRefID", "options": "B"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.lineWidth", "value": 2}, {"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"matcher": {"id": "byFrameRefID", "options": "C"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.lineWidth", "value": 2}, {"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 34, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{namespace=\"$namespace\", pod=~\"^ews-player-engagement-cms.*\", container!=\"\"}) by (container)", "hide": false, "interval": "", "legendFormat": "{{container}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(kube_pod_container_resource_requests{job=\"kube-state-metrics\", namespace=\"$namespace\", pod=~\"^ews-player-engagement-cms.*\", resource=\"cpu\"})", "hide": false, "instant": false, "interval": "", "legendFormat": "requests", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(\r\n    kube_pod_container_resource_limits{job=\"kube-state-metrics\", namespace=\"$namespace\", pod=~\"^ews-player-engagement-cms.*\", resource=\"cpu\"}\r\n)\r\n", "hide": false, "instant": false, "interval": "", "legendFormat": "limits", "range": true, "refId": "C"}], "title": "CPU", "transparent": true, "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "byFrameRefID", "options": "B"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.lineWidth", "value": 2}, {"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "byFrameRefID", "options": "C"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.lineWidth", "value": 2}, {"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 35, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(container_memory_working_set_bytes{job=\"kubelet\", metrics_path=\"/metrics/cadvisor\", namespace=\"$namespace\", pod=~\"^ews-player-engagement-cms.*\", container!=\"\", image!=\"\"}) by (container)", "hide": false, "interval": "", "legendFormat": "{{container}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(kube_pod_container_resource_requests{job=\"kube-state-metrics\", namespace=\"$namespace\", pod=~\"^ews-player-engagement-cms.*\", resource=\"memory\"})\r\n", "hide": false, "interval": "", "legendFormat": "requests", "range": true, "refId": "B"}], "title": "Memory", "transparent": true, "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 16}, "id": 38, "interval": "1m", "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "rate(npgsql_db_client_commands_bytes_written{job=\"ews-player-engagement-cms\", namespace=\"$namespace\"}[$__interval])", "interval": "", "legendFormat": "Bytes written", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "rate(npgsql_db_client_commands_bytes_read{job=\"$job\", namespace=\"$namespace\"}[$__interval])", "hide": false, "interval": "", "legendFormat": "Bytes read", "range": true, "refId": "B"}], "title": "Postgres I/O", "transparent": true, "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "purple", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 16}, "id": 39, "interval": "1m", "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "process_num_threads{namespace=\"$namespace\", container=\"ews-player-engagement-cms\"}", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "interval": "", "legendFormat": "{{container}} process threads count", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "system_runtime_threadpool_thread_count{container=\"ews-player-engagement-cms\", namespace=\"$namespace\", container=\"ews-player-engagement-cms\"}", "hide": false, "instant": false, "interval": "", "legendFormat": "{{container}} threadpool threads count", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "system_runtime_threadpool_queue_length{namespace=\"$namespace\", container=\"ews-player-engagement-cms\"}", "hide": false, "instant": false, "interval": "", "legendFormat": "{{container}} threadpool queue length", "range": true, "refId": "C"}], "title": "Threads", "transparent": true, "type": "timeseries"}, {"datasource": null, "description": "GC Committed bytes indicates the total committed memory on the GC heap at that time. \nValue of the total committed bytes is a bit bigger than the sum of gen 0 heap size + gen 1 heap size + gen 2 heap size + LOH size.\n\ngc-heap-size excludes all accounting for fragmentation as well as other committed memory that is being managed by the GC such as capacity for new allocations in the near future.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decmbytes"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 16}, "id": 40, "interval": "1m", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"disableTextWrap": false, "editorMode": "code", "expr": "system_runtime_gc_committed{namespace=\"$namespace\", container=\"ews-player-engagement-cms\"}", "fullMetaSearch": false, "includeNullMetadata": true, "interval": "", "legendFormat": "{{container}} GC commited bytes", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "system_runtime_gc_heap_size{namespace=\"$namespace\", container=\"ews-player-engagement-cms\"}", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "interval": "", "legendFormat": "{{container}} gc heap size", "range": true, "refId": "B", "useBackend": false}], "title": "GC commited bytes and GC heap size", "type": "timeseries"}], "refresh": "", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": true, "text": "ews-player-engagement-cms", "value": "ews-player-engagement-cms"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{}, job)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Job", "multi": false, "name": "job", "options": [], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/ews-player-engagement-cms.*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-sof-prod", "value": "ews-sof-prod"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "***********:80", "value": "***********:80"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Instance", "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": "ews-player-engagement-cms-7458b84b65-cfv8q", "value": "ews-player-engagement-cms-7458b84b65-cfv8q"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Pod", "multi": false, "name": "pod", "options": [{"selected": true, "text": "ews-player-engagement-cms-7458b84b65-cfv8q", "value": "ews-player-engagement-cms-7458b84b65-cfv8q"}, {"selected": false, "text": "ews-player-engagement-cms-7458b84b65-fszlx", "value": "ews-player-engagement-cms-7458b84b65-fszlx"}, {"selected": false, "text": "ews-player-engagement-cms-7458b84b65-lb4z6", "value": "ews-player-engagement-cms-7458b84b65-lb4z6"}, {"selected": false, "text": "ews-player-engagement-cms-7458b84b65-5rqmf", "value": "ews-player-engagement-cms-7458b84b65-5rqmf"}], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-player-engagement-cms-7458b84b65-cfv8q", "value": "ews-player-engagement-cms-7458b84b65-cfv8q"}, "datasource": null, "definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Podname", "multi": false, "name": "podname", "options": [{"selected": true, "text": "ews-player-engagement-cms-7458b84b65-cfv8q", "value": "ews-player-engagement-cms-7458b84b65-cfv8q"}], "query": {"query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "player-engagement-cms", "uid": "b8700b1440dgfcsb8019k", "version": 3}