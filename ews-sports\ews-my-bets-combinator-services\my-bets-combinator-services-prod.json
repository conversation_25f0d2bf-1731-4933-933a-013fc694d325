{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "iteration": 1708075196277, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 57, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(http_request_duration_seconds_count{job=\"$job\",namespace=\"$namespace\", action != \"\"}[$__rate_interval])) by (action,code,controller)", "interval": "", "legendFormat": "{{action}} [{{code}}] {{controller}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Http request duration by endpoint", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:755", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:756", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 42, "panels": [], "title": "General", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "hiddenSeries": false, "id": 59, "legend": {"avg": true, "current": false, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(CMSClient_histogram_sum{namespace=\"$namespace\",job=\"$job\"}[1m])/\r\nrate(CMSClient_histogram_count{namespace=\"$namespace\",job=\"$job\"}[1m])*10000", "interval": "", "legendFormat": "{{instance}} - {{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Histogram for CMSApiClient", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:98", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:99", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 11, "x": 12, "y": 10}, "hiddenSeries": false, "id": 61, "legend": {"avg": true, "current": false, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(sportsapi_latency_sum{namespace=\"$namespace\",job=\"$job\"}[1m])/\r\nrate(sportsapi_latency_count{namespace=\"$namespace\",job=\"$job\"}[1m])*10000", "interval": "", "legendFormat": "{{instance}} - {{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Histogram for SportsApiClient", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:98", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:99", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 18}, "hiddenSeries": false, "id": 63, "legend": {"avg": true, "current": false, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(lightWeightRiskService_latency_sum{namespace=\"$namespace\",job=\"$job\"}[1m])/\r\nrate(lightWeightRiskService_latency_count{namespace=\"$namespace\",job=\"$job\"}[1m])*10000", "interval": "", "legendFormat": "{{instance}} - {{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Histogram for LightWeightRiskService(Trading)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:98", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:99", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 11, "x": 12, "y": 18}, "hiddenSeries": false, "id": 62, "legend": {"avg": true, "current": false, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(LiabilityClient_histogram_sum{namespace=\"$namespace\",job=\"$job\"}[1m])/\r\nrate(LiabilityClient_histogram_count{namespace=\"$namespace\",job=\"$job\"}[1m])*10000", "interval": "", "legendFormat": "{{instance}} - {{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Histogram for LiabilityApiClient", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:98", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:99", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 26}, "hiddenSeries": false, "id": 34, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(execution_failed_total{job=\"ews-mybets-service\",namespace=\"$namespace\"}[1m])) by (controller,action,pod)", "interval": "", "legendFormat": "{{controller}}.{{action}} ({{pod}})", "refId": "A"}, {"expr": "sum(rate(execution_failed_total{job=\"ews-combinator-net-api\",namespace=\"$namespace\"}[1m])) by  (controller,action,pod)", "hide": false, "interval": "", "legendFormat": "{{controller}}.{{action}} ({{pod}})", "refId": "B"}, {"expr": "sum(rate(count_errors{job=\"ews-mybets-service\",namespace=\"$namespace\"}[1m])) by (type,pod)", "hide": false, "interval": "", "legendFormat": "{{type}} ({{pod}})", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "ERRORS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:224", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:225", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 26}, "hiddenSeries": false, "id": 48, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(aspnetcore_healthcheck_status{job=\"ews-mybets-service\",namespace=\"$namespace\"}) by (pod,name)", "interval": "", "legendFormat": "{{name}} {{pod}}", "refId": "A"}, {"expr": "sum(aspnetcore_healthcheck_status{job=\"ews-combinator-net-api\",namespace=\"$namespace\"}) by (pod,name)", "hide": false, "interval": "", "legendFormat": "{{name}} {{pod}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Healthchecks", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:224", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:225", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 5, "x": 12, "y": 26}, "hiddenSeries": false, "id": 47, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (hub) (sum(gauge_websocket_users{job=\"ews-mybets-service\",namespace=\"$namespace\"}))", "interval": "", "legendFormat": "Websocket Sessions", "refId": "A"}, {"expr": "sum by (hub) (sum(gauge_websocket_unique_players{job=\"ews-mybets-service\",namespace=\"$namespace\"}))", "hide": false, "interval": "", "legendFormat": "Unique Players WS Sessions", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Sessions Count (Websocket Users)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:224", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:225", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 17, "y": 26}, "hiddenSeries": false, "id": 49, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(cache_items_count{job=\"ews-mybets-service\",namespace=\"$namespace\"}) by (cache)", "interval": "", "legendFormat": "{{cache}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON><PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:224", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:225", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 33}, "hiddenSeries": false, "id": 36, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(execution_duration_seconds_sum{job=\"ews-mybets-service\",namespace=\"$namespace\"}[5m])/rate(execution_duration_seconds_count{job=\"ews-mybets-service\",namespace=\"$namespace\"}[5m])) by (controller,action,job)", "hide": false, "interval": "", "legendFormat": "MyBets.{{controller}}.{{action}}", "refId": "A"}, {"expr": "sum(rate(execution_duration_seconds_sum{job=\"ews-combinator-net-api\",namespace=\"$namespace\"}[5m])/rate(execution_duration_seconds_count{job=\"ews-combinator-net-api\",namespace=\"$namespace\"}[5m])) by (controller,action,job)", "hide": false, "interval": "", "legendFormat": "Combinator.{{controller}}.{{action}}", "refId": "B"}, {"expr": "avg(rate(http_request_duration_seconds_sum{job=\"ews-mybets-service\",namespace=\"$namespace\", action != \"\"}[5m]) / rate(http_request_duration_seconds_count{job=\"ews-mybets-service\",namespace=\"$namespace\", action != \"\"}[5m])) by (action,code,controller)", "hide": false, "interval": "", "legendFormat": "{{controller}}.{{action}} [{{code}}]", "refId": "C"}, {"expr": "avg(rate(http_request_duration_seconds_sum{job=\"ews-combinator-net-api\",namespace=\"$namespace\", action != \"\"}[5m]) / rate(http_request_duration_seconds_count{job=\"ews-combinator-net-api\",namespace=\"$namespace\", action != \"\"}[5m])) by (action,code,controller)", "hide": false, "interval": "", "legendFormat": "Cimbinator.{{controller}}.{{action}} [{{code}}]", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Execution Durations in Seconds", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:546", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:547", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 41}, "hiddenSeries": false, "id": 38, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(execution_duration_seconds_count{job=\"ews-mybets-service\",namespace=\"$namespace\"}[1m])) by (controller,action,job)", "interval": "", "legendFormat": "{{job}}.{{controller}}.{{action}}", "refId": "A"}, {"expr": "sum(rate(execution_duration_seconds_count{job=\"ews-combinator-net-api\",namespace=\"$namespace\"}[1m])) by (controller,action,job)", "hide": false, "interval": "", "legendFormat": "{{job}}.{{controller}}.{{action}}", "refId": "B"}, {"expr": "sum(rate(http_request_duration_seconds_count{job=\"ews-mybets-service\",namespace=\"$namespace\", action != \"\"}[1m])) by (action,code,controller)", "hide": false, "interval": "", "legendFormat": "{{action}} [{{code}}] {{controller}}", "refId": "C"}, {"expr": "sum(rate(http_request_duration_seconds_count{job=\"ews-combinator-net-api\",namespace=\"$namespace\", action != \"\"}[1m])) by (action,code,controller)", "hide": false, "interval": "", "legendFormat": "{{action}} [{{code}}] {{controller}}", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Execution Rates", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 50}, "hiddenSeries": false, "id": 55, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "(sum(rate(histogram_get_player_bets_exec_duration_sum{job=\"ews-mybets-service\",namespace=\"$namespace\",operation=\"Open Bets\"}[5m])) /\n sum(rate(histogram_get_player_bets_exec_duration_count{job=\"ews-mybets-service\",namespace=\"$namespace\",operation=\"Open Bets\"}[5m]))) -\n(sum(rate(histogram_get_player_bets_exec_duration_sum{job=\"ews-mybets-service\",namespace=\"$namespace\",operation=\"open bets cashout calculation\"}[5m])) / sum(rate(histogram_get_player_bets_exec_duration_count{job=\"ews-mybets-service\",namespace=\"$namespace\",operation=\"open bets cashout calculation\"}[5m])))", "hide": false, "interval": "", "legendFormat": "Open Bets", "refId": "B"}, {"expr": "sum(rate(histogram_get_player_bets_exec_duration_sum{job=\"ews-mybets-service\",namespace=\"$namespace\",operation=\"Closed Bets\"}[5m])) / sum(rate(histogram_get_player_bets_exec_duration_count{job=\"ews-mybets-service\",namespace=\"$namespace\",operation=\"Closed Bets\"}[5m]))", "hide": false, "interval": "", "legendFormat": "Closed Bets", "refId": "D"}, {"expr": "sum(rate(histogram_get_player_bets_exec_duration_sum{job=\"ews-mybets-service\",namespace=\"$namespace\",operation=\"open bets cashout calculation\"}[5m])) / sum(rate(histogram_get_player_bets_exec_duration_count{job=\"ews-mybets-service\",namespace=\"$namespace\",operation=\"open bets cashout calculation\"}[5m]))", "hide": false, "interval": "", "legendFormat": "open bets cashout calculation", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Player Bets Calculations execution duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:546", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:547", "format": "Computation", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 59}, "hiddenSeries": false, "id": 14, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(count_processed_updates{job=\"ews-mybets-service\",namespace=\"$namespace\"}[1m])) \n", "interval": "", "legendFormat": "Total", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Processed Updates", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:755", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:756", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"color": {}, "custom": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 67}, "hiddenSeries": false, "id": 53, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(gauge_expired_cache_items{job=\"ews-mybets-service\",namespace=\"$namespace\"}[1m])) by (cache)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{cache}} ", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Expired Items Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 75}, "hiddenSeries": false, "id": 52, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(count_produced_messages{job=\"ews-mybets-service\",namespace=\"$namespace\"}[1m])) by (type)\n", "interval": "", "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Produced Messages", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:755", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:756", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"color": {}, "custom": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 85}, "hiddenSeries": false, "id": 54, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(gauge_expired_cache_items{job=\"ews-mybets-service\",namespace=\"$namespace\"}) by (cache)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{cache}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Expired Items Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 92}, "id": 22, "panels": [], "title": "Resources", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 93}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(process_cpu_seconds_total{job=\"ews-mybets-service\",namespace=\"$namespace\"}[5m])) by (pod)", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "My Bets Service - CPU Cores Used", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:91", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:92", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 93}, "hiddenSeries": false, "id": 24, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_private_memory_bytes{job=\"ews-mybets-service\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "My Bets Service - Private Memory Bytes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:585", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:586", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 93}, "hiddenSeries": false, "id": 40, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(process_cpu_seconds_total{job=\"ews-combinator-net-api\",namespace=\"$namespace\"}[5m])) by (pod)", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Combinator - CPU Cores Used", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:91", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:92", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 93}, "hiddenSeries": false, "id": 39, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(process_private_memory_bytes{job=\"ews-combinator-net-api\",namespace=\"$namespace\"}) by (pod)", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Combinator NET - Private Memory Bytes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:585", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:586", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 100}, "hiddenSeries": false, "id": 26, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_open_handles{job=\"ews-mybets-service\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "My Bets Service - Open Handles", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:895", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:896", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 100}, "hiddenSeries": false, "id": 28, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_num_threads{job=\"ews-mybets-service\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "My Bets Service - Process Threads", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:977", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:978", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 100}, "hiddenSeries": false, "id": 43, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(process_open_handles{job=\"ews-combinator-net-api\",namespace=\"$namespace\"}) by (pod)", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Combinator NET - Open Handles", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:895", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:896", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 100}, "hiddenSeries": false, "id": 44, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(process_num_threads{job=\"ews-combinator-net-api\",namespace=\"$namespace\"}) by (pod)", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Combinator NET - Process Threads", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:977", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:978", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 108}, "hiddenSeries": false, "id": 30, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_start_time_seconds{job=\"ews-mybets-service\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "My Bets Service - Start Time Seconds", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1059", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1060", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 108}, "hiddenSeries": false, "id": 32, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_working_set_bytes{job=\"ews-mybets-service\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "My Bets Service - Working Set Bytes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1141", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1142", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 108}, "hiddenSeries": false, "id": 45, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(process_start_time_seconds{job=\"ews-websocket-mgnt-net\",namespace=\"$namespace\"}) by (pod)", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Combinator NET -  Start Time Seconds", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1059", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1060", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 108}, "hiddenSeries": false, "id": 46, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(process_working_set_bytes{job=\"ews-combinator-net-api\",namespace=\"$namespace\"}) by (pod)", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Combinator NET - Working Set Bytes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1141", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1142", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": "all", "current": {"selected": false, "text": "ews-mybets-service", "value": "ews-mybets-service"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{}, job)", "description": null, "error": null, "hide": 2, "includeAll": false, "label": "Job", "multi": false, "name": "job", "options": [], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/ews-mybets-service.*|ews-combinator-net-api.*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-dev", "value": "ews-dev"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "description": null, "error": null, "hide": 2, "includeAll": true, "label": "Instance", "multi": true, "name": "instance", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-mybets-service-8658559f54-fpsgl", "value": "ews-mybets-service-8658559f54-fpsgl"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "description": null, "error": null, "hide": 2, "includeAll": false, "label": "Pod", "multi": false, "name": "pod", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-mybets-service-8658559f54-fpsgl", "value": "ews-mybets-service-8658559f54-fpsgl"}, "datasource": null, "definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "description": null, "error": null, "hide": 2, "includeAll": false, "label": "Podname", "multi": false, "name": "podname", "options": [], "query": {"query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "My Bets & Combinator Services", "uid": "XuDOHs_nk", "version": 2}