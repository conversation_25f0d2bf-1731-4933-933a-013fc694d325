{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 3683, "links": [], "panels": [{"datasource": {"type": "prometheus"}, "description": "Affiliate and promotions module events per bu. Keeps track of happening in the module", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 0}, "id": 32, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (business_unit,event_type) (increase(affiliate_event_counter{job=\"ews-affiliate-module\", namespace=\"$namespace\"}[1m])) ", "interval": "", "legendFormat": "{{business_unit}} - {{event_type}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (business_unit,event_type) (increase(promotions_event_counter{job=\"ews-affiliate-module\", namespace=\"$namespace\"}[1m])) ", "hide": false, "interval": "", "legendFormat": "{{business_unit}} - {{event_type}}", "range": true, "refId": "B"}], "title": "Affiliate/Promotion events by BU", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Background job activity tracker, to see if background service is working", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["Background Activity"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 0}, "id": 30, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by () (increase(background_service_activity{job=\"ews-affiliate-module\", namespace=\"$namespace\"}[1m])) ", "interval": "", "legendFormat": "Background Activity", "range": true, "refId": "A"}], "title": "Background job activity", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Tracks calls made to external services and response codes from each. Helps track issues with external services", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 0}, "id": 26, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (external_api_with_endpoint,status_code) (increase(external_api_with_counter{job=\"ews-affiliate-module\", namespace=\"$namespace\"}[1m])) ", "interval": "", "legendFormat": "\"{{external_api_with_endpoint}}\" call -  {{status_code}}", "range": true, "refId": "A"}], "title": "External services tracker", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Counter by business unit and error types. It shows what kind of error happened when affiliate tried to register for specific BU", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 6}, "id": 2, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (business_unit,error_type) (increase(affiliate_registration_errors_counter{job=\"ews-affiliate-module\", namespace=\"$namespace\"}[1m]))", "interval": "", "legendFormat": "{{business_unit}} - {{error_type}}", "range": true, "refId": "A"}], "title": "Registration errors per BU", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Shows error occurred during background sync. Errors are summed by type and business unit", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 6}, "id": 14, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (business_unit,event_type) (increase(background_service_errors_counter{job=\"ews-affiliate-module\", namespace=\"$namespace\"}[1m]))", "interval": "", "legendFormat": "{{business_unit}} - {{event_type}}", "range": true, "refId": "A"}], "title": "Background job errors by BU", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Errors that happened during any operation over promotions per BU", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 6}, "id": 28, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (business_unit,error_type) (increase(promotions_errors_counter{job=\"ews-affiliate-module\", namespace=\"$namespace\"}[1m]))", "interval": "", "legendFormat": "{{business_unit}} - {{error_type}}", "range": true, "refId": "A"}], "title": "Promotions errors per BU", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 14}, "id": 33, "panels": [], "title": "Common Errors", "type": "row"}, {"datasource": {"type": "prometheus"}, "description": "Unhandled errors that resulted in 500 and also kafka errors tracked from common library", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 15}, "id": 24, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by () (increase(error_count{job=\"ews-affiliate-module\", namespace=\"$namespace\"}[1m]))", "interval": "", "legendFormat": "General errors", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by () (increase(kafka_errors{job=\"ews-affiliate-module\", namespace=\"$namespace\"}[1m]))", "hide": false, "interval": "", "legendFormat": "<PERSON><PERSON><PERSON> errors", "range": true, "refId": "B"}], "title": "Errors", "type": "timeseries"}], "preload": false, "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "ews-affiliate-module", "value": "ews-affiliate-module"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{}, job)", "includeAll": false, "label": "Job", "name": "job", "options": [], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/ews-affiliate-module.*/", "type": "query"}, {"current": {"text": "ews-dev", "value": "ews-dev"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "includeAll": false, "label": "Namespace", "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"isNone": true, "text": "None", "value": ""}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "includeAll": false, "label": "Instance", "name": "instance", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"current": {"isNone": true, "text": "None", "value": ""}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "hide": 2, "includeAll": false, "label": "Pod", "name": "pod", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"isNone": true, "text": "None", "value": ""}, "datasource": {"type": "prometheus"}, "definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "includeAll": false, "label": "Podname", "name": "podname", "options": [], "query": {"query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "affiliate-module", "uid": "a5bc403074b14592bc09", "version": 1, "weekStart": ""}