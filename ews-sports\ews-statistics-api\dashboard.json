{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 4845, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 12, "panels": [], "title": "General", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 2, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(execution_failed_total{job=\"$job\",namespace=\"$namespace\"}[1m])) by (controller,action,pod)", "interval": "", "legendFormat": "{{controller}}.{{action}} ({{pod}})", "refId": "A"}], "title": "ERRORS", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 4, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(aspnetcore_healthcheck_status{job=\"$job\",namespace=\"$namespace\"}) by (pod,name)", "interval": "", "legendFormat": "{{name}} {{pod}}", "refId": "A"}], "title": "Healthchecks", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 6, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(execution_duration_seconds_sum{job=\"$job\",namespace=\"$namespace\"}[5m])/rate(execution_duration_seconds_count{job=\"$job\",namespace=\"$namespace\"}[5m])) by (controller,action,job)", "interval": "", "legendFormat": "{{job}}.{{controller}}.{{action}}", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "sum(rate(http_request_duration_seconds_sum{job=\"$job\",namespace=\"$namespace\", action != \"\"}[5m])) by (action,code,controller) / sum(rate(http_request_duration_seconds_count{job=\"$job\",namespace=\"$namespace\", action != \"\"}[5m])) by (action,code,controller)", "hide": false, "interval": "", "legendFormat": "{{controller}}.{{action}} [{{code}}]", "refId": "B"}], "title": "Execution Durations in Seconds", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "id": 8, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(execution_duration_seconds_count{job=\"$job\",namespace=\"$namespace\"}[$__rate_interval])) by (controller,action,job)", "interval": "", "legendFormat": "{{job}}.{{controller}}.{{action}}", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "sum(rate(http_request_duration_seconds_count{job=\"$job\",namespace=\"$namespace\", action != \"\"}[$__rate_interval])) by (action,code,controller)", "hide": false, "interval": "", "legendFormat": "{{action}} [{{code}}] {{controller}}", "refId": "B"}], "title": "Execution Rates", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "id": 10, "panels": [], "title": "Resources", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 18}, "id": 14, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(process_cpu_seconds_total{job=\"$job\",namespace=\"$namespace\"}[5m])) by (pod)", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "CPU Cores Used", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 18}, "id": 18, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_private_memory_bytes{job=\"$job\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Private Memory Bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 18}, "id": 19, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_num_threads{job=\"$job\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Process Threads", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 18}, "id": 20, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_working_set_bytes{job=\"$job\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Working Set Bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 26}, "id": 23, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_open_handles{job=\"$job\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Open Handles", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 26}, "id": 24, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_start_time_seconds{job=\"$job\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Start Time Seconds", "type": "timeseries"}], "preload": false, "refresh": "", "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "", "value": ""}, "label": "Feed Type", "name": "feed_type", "options": [{"selected": true, "text": "Premium", "value": ""}, {"selected": false, "text": "Standard", "value": "standard-"}], "query": "Premium :   ,Standard : standard-", "type": "custom"}, {"current": {"text": "ews-statistics-api", "value": "ews-statistics-api"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{}, job)", "includeAll": false, "label": "Job", "name": "job", "options": [], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/^${feed_type}ews-statistics-api.*/", "type": "query"}, {"current": {"text": "ews-uat", "value": "ews-uat"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "includeAll": false, "label": "Namespace", "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"current": {"text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "includeAll": true, "label": "Instance", "multi": true, "name": "instance", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"current": {"text": "ews-statistics-api-5c7f55db5-zh7d7", "value": "ews-statistics-api-5c7f55db5-zh7d7"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "includeAll": false, "label": "pod", "name": "pod", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "ews-statistics-api-5c7f55db5-zh7d7", "value": "ews-statistics-api-5c7f55db5-zh7d7"}, "datasource": {"type": "prometheus"}, "definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "hide": 2, "includeAll": false, "label": "Podname", "name": "podname", "options": [], "query": {"query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Statistics Api", "uid": "zWoiiNJ4k", "version": 1, "weekStart": ""}