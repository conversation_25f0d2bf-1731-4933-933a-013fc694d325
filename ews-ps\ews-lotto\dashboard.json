{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 4121, "links": [], "panels": [{"datasource": {"type": "elasticsearch", "uid": "L-uYOi7Hz"}, "description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "red", "mode": "fixed"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 0}, "id": 72, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"alias": "", "bucketAggs": [{"id": "2", "settings": {"interval": "auto"}, "type": "date_histogram"}], "datasource": {"type": "elasticsearch", "uid": "L-uYOi7Hz"}, "metrics": [{"id": "1", "type": "count"}], "query": "kubernetes.labels.io.kompose.service:\"ews-lotto-api\" AND kubernetes.namespace_name:\"$namespace\" AND message:\"fail:\"", "refId": "A", "timeField": "@timestamp"}], "title": "Api - Errors", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "elasticsearch", "uid": "L-uYOi7Hz"}, "description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "semi-dark-red", "mode": "fixed"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 2, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 5, "w": 12, "x": 12, "y": 0}, "id": 81, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"alias": "", "bucketAggs": [{"field": "@timestamp", "id": "2", "settings": {"interval": "10m", "min_doc_count": "0", "trimEdges": "0"}, "type": "date_histogram"}], "datasource": {"type": "elasticsearch", "uid": "L-uYOi7Hz"}, "hide": false, "metrics": [{"id": "5", "type": "count"}], "query": "kubernetes.labels.io.kompose.service:\"ews-lotto-distributor\" AND kubernetes.namespace_name:\"$namespace\" AND message:\"fail:\"", "refId": "A", "timeField": "@timestamp"}], "title": "Distributor - Errors", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 5}, "id": 39, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "increase(http_requests_received_total{\njob=\"ews-lotto-api\", \nnamespace=\"$namespace\", \nendpoint=~\".*/agent/.*|.*/player/.*\"\n}[$__interval])", "interval": "", "legendFormat": "{{method}} {{endpoint}}", "range": true, "refId": "A"}], "title": "Api - Throughput", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "light-blue", "mode": "fixed"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 12, "y": 5}, "id": 53, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum by (service) (increase(kafka_consumed_count{service=\"distributor_service\",namespace=\"$namespace\"}[$__interval]))", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "Distributor service consumer", "refId": "A"}], "title": "Distributor - Consumed Events", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "light-green", "mode": "fixed"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 16, "y": 5}, "id": 66, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum by (service) (increase(kafka_produced_count{service=\"distributor_service\",namespace=\"$namespace\"}[$__interval]))", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "Distributor service producer", "refId": "A"}], "title": "Distributor - Produced Events", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "light-blue", "mode": "fixed"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 20, "y": 5}, "id": 57, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (service)(increase(kafka_consumed_count{service=\"api_service\",namespace=\"$namespace\"}[$__interval]))", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "Api consumer", "refId": "A"}], "title": "Api - Consumed Events", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 13}, "id": 34, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": ["max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Max", "sortDesc": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "max by (endpoint) (histogram_quantile(1,increase(http_request_duration_seconds_bucket{job=\"ews-lotto-api\",namespace=\"$namespace\",endpoint!=\"api/lotto/health\"}[$__interval])))", "hide": false, "interval": "", "legendFormat": "{{method}} {{endpoint}}", "range": true, "refId": "A"}], "title": "Api - Latency", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "semi-dark-red", "mode": "fixed"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 12, "y": 13}, "id": 60, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (service) (increase(kafka_process_error_count{service=\"distributor_service\",namespace=\"$namespace\"}[$__interval]))", "interval": "", "intervalFactor": 1, "legendFormat": "", "range": true, "refId": "A"}], "title": "Distributor - Consumed Errors", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "semi-dark-red", "mode": "fixed"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 16, "y": 13}, "id": 56, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (service) (increase(kafka_produce_error_count{service=\"distributor_service\",namespace=\"$namespace\"}[$__interval]))", "interval": "", "intervalFactor": 1, "legendFormat": "Distributor producer errors", "range": true, "refId": "A"}], "title": "Distributor - Produced Errors", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "semi-dark-red", "mode": "fixed"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 20, "y": 13}, "id": 59, "interval": "1m", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (service) (increase(kafka_process_error_count{service=\"api_service\",namespace=\"$namespace\"}[$__interval]))", "interval": "", "intervalFactor": 1, "legendFormat": "Api consumer errors", "range": true, "refId": "A"}], "title": "Api  - Consumed Errors", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Displaying the CPU usage of the ews-lotto-api containers as a percentage of their allocated CPU resources. The query calculates the average CPU usage over the last 5 minutes and compares it to the CPU allocation defined for each container. Values above 100% indicate the container is using more CPU than what has been allocated to it.", "fieldConfig": {"defaults": {"color": {"fixedColor": "semi-dark-green", "mode": "fixed"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "byFrameRefID", "options": "B"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.lineWidth", "value": 2}, {"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"matcher": {"id": "byFrameRefID", "options": "C"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.lineWidth", "value": 2}, {"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 21}, "id": 74, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{namespace=\"$namespace\", pod=~\"^ews-lotto-api.*\", container!=\"\"}) by (container)", "hide": false, "interval": "", "legendFormat": "{{container}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(kube_pod_container_resource_requests{job=\"kube-state-metrics\", namespace=\"$namespace\", pod=~\"^ews-lotto-api.*\", resource=\"cpu\"})", "hide": false, "instant": false, "interval": "", "legendFormat": "requests", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(\r\n    kube_pod_container_resource_limits{job=\"kube-state-metrics\", namespace=\"$namespace\", pod=~\"^ews-lotto-api.*\", resource=\"cpu\"}\r\n)\r\n", "hide": false, "instant": false, "interval": "", "legendFormat": "limits", "range": true, "refId": "C"}], "title": "Api - CPU", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Displaying the CPU usage of the ews-lotto-api containers as a percentage of their allocated CPU resources. The query calculates the average CPU usage over the last 5 minutes and compares it to the CPU allocation defined for each container. Values above 100% indicate the container is using more CPU than what has been allocated to it.", "fieldConfig": {"defaults": {"color": {"fixedColor": "semi-dark-green", "mode": "fixed"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "byFrameRefID", "options": "B"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.lineWidth", "value": 2}, {"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"matcher": {"id": "byFrameRefID", "options": "C"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.lineWidth", "value": 2}, {"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 21}, "id": 100, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{namespace=\"$namespace\", pod=~\"^ews-lotto-distributor.*\", container!=\"\"}) by (container)", "hide": false, "interval": "", "legendFormat": "{{container}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(kube_pod_container_resource_requests{job=\"kube-state-metrics\", namespace=\"$namespace\", pod=~\"^ews-lotto-distributor.*\", resource=\"cpu\"})", "hide": false, "instant": false, "interval": "", "legendFormat": "requests", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(\r\n    kube_pod_container_resource_limits{job=\"kube-state-metrics\", namespace=\"$namespace\", pod=~\"^ews-lotto-distributor.*\", resource=\"cpu\"}\r\n)\r\n", "hide": false, "instant": false, "interval": "", "legendFormat": "limits", "range": true, "refId": "C"}], "title": "Distributor- CPU", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "byFrameRefID", "options": "B"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.lineWidth", "value": 2}, {"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "byFrameRefID", "options": "C"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.lineWidth", "value": 2}, {"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 29}, "id": 51, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(container_memory_working_set_bytes{job=\"kubelet\", metrics_path=\"/metrics/cadvisor\", namespace=\"$namespace\", pod=~\"^ews-lotto-api.*\", container!=\"\", image!=\"\"}) by (container)", "hide": false, "interval": "", "legendFormat": "{{container}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(kube_pod_container_resource_requests{job=\"kube-state-metrics\", namespace=\"$namespace\", pod=~\"^ews-lotto-api.*\", resource=\"memory\"})\r\n", "hide": false, "interval": "", "legendFormat": "requests", "range": true, "refId": "B"}], "title": "Api - Memory", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "byFrameRefID", "options": "B"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.lineWidth", "value": 2}, {"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "byFrameRefID", "options": "C"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.lineWidth", "value": 2}, {"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 29}, "id": 101, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(container_memory_working_set_bytes{job=\"kubelet\", metrics_path=\"/metrics/cadvisor\", namespace=\"$namespace\", pod=~\"^ews-lotto-distributor.*\", container!=\"\", image!=\"\"}) by (container)", "hide": false, "interval": "", "legendFormat": "{{container}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(kube_pod_container_resource_requests{job=\"kube-state-metrics\", namespace=\"$namespace\", pod=~\"^ews-lotto-distributor.*\", resource=\"memory\"})\r\n", "hide": false, "interval": "", "legendFormat": "requests", "range": true, "refId": "B"}], "title": "Distributor- Memory", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 37}, "id": 43, "interval": "1m", "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(npgsql_db_client_commands_bytes_written{job=\"ews-lotto-api\", namespace=\"$namespace\"}[$__interval])", "interval": "", "legendFormat": "Bytes written", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(npgsql_db_client_commands_bytes_read{job=\"$job\", namespace=\"$namespace\"}[$__interval])", "hide": false, "interval": "", "legendFormat": "Bytes read", "range": true, "refId": "B"}], "title": "Api - Postgres I/O", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "purple", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 37}, "id": 84, "interval": "1m", "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "process_num_threads{namespace=\"$namespace\", container=\"ews-lotto-api\"}", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "interval": "", "legendFormat": "{{container}} process threads count", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "system_runtime_threadpool_thread_count{container=\"ews-lotto-api\", namespace=\"$namespace\", container=\"ews-lotto-api\"}", "hide": false, "instant": false, "interval": "", "legendFormat": "{{container}} threadpool threads count", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "system_runtime_threadpool_queue_length{namespace=\"$namespace\", container=\"ews-lotto-api\"}", "hide": false, "instant": false, "interval": "", "legendFormat": "{{container}} threadpool queue length", "range": true, "refId": "C"}], "title": "Api - Threads", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "GC Committed bytes indicates the total committed memory on the GC heap at that time. \nValue of the total committed bytes is a bit bigger than the sum of gen 0 heap size + gen 1 heap size + gen 2 heap size + LOH size.\n\ngc-heap-size excludes all accounting for fragmentation as well as other committed memory that is being managed by the GC such as capacity for new allocations in the near future.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decmbytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 37}, "id": 99, "interval": "1m", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"disableTextWrap": false, "editorMode": "code", "expr": "system_runtime_gc_committed{namespace=\"$namespace\", container=\"ews-lotto-api\"}", "fullMetaSearch": false, "includeNullMetadata": true, "interval": "", "legendFormat": "{{container}} GC commited bytes", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "system_runtime_gc_heap_size{namespace=\"$namespace\", container=\"ews-lotto-api\"}", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "interval": "", "legendFormat": "{{container}} gc heap size", "range": true, "refId": "B", "useBackend": false}], "title": "Api - GC commited bytes and GC heap size", "type": "timeseries"}], "preload": false, "refresh": "", "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "ews-lotto-api", "value": "ews-lotto-api"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{}, job)", "hide": 2, "includeAll": false, "label": "Job", "name": "job", "options": [], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/ews-lotto-api.*/", "type": "query"}, {"current": {"text": "ews-dev", "value": "ews-dev"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "includeAll": false, "label": "Namespace", "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-7d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "ews-lotto", "uid": "auhYQV4Nkas", "version": 2, "weekStart": ""}