{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 4065, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 38, "panels": [], "title": "General", "type": "row"}, {"datasource": {"type": "prometheus"}, "description": "Consumed Messages from payment-transfers topic count by business unit and message type.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 1}, "id": 34, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (message_type,BU) (increase(consumed_messages_count{job=\"ews-integrator-data-processing\", topic=\"payment-transfers\", namespace=\"$namespace\"}[5m])) > 0", "interval": "", "legendFormat": "{{BU}}-{{message_type}}", "range": true, "refId": "A"}], "title": "Consumed messages - payment-transfers", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Consumed messages from platform.accounting.transactions count by business unit and message type.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 1}, "id": 42, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (message_type,BU) (increase(consumed_messages_count{job=\"ews-integrator-data-processing\", topic=\"platform.accounting.transactions\", namespace=\"$namespace\"}[5m])) > 0", "interval": "", "legendFormat": "{{BU}}-{{message_type}}", "range": true, "refId": "A"}], "title": "Consumed messages - platform.accounting.transactions", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Processed messages count by business unit and message type.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 8}, "id": 2, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (BU,message_type) (increase(processed_messages_count{job=\"ews-integrator-data-processing\", namespace=\"$namespace\"}[5m])) > 0", "interval": "", "legendFormat": "{{BU}}-{{message_type}}", "range": true, "refId": "A"}], "title": "Processed messages", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Produced messages count by business unit and message type.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 8}, "id": 35, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (BU,message_type) (increase(produced_messages_count{job=\"ews-integrator-data-processing\", namespace=\"$namespace\"}[5m])) > 0", "interval": "", "legendFormat": "{{BU}}-{{message_type}}", "range": true, "refId": "A"}], "title": "Produced messages", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Tracks total exceptions thrown in the app regardless from where or what. General error counter.\nAlso errors happened when producing messages to <PERSON><PERSON><PERSON> separated by topic and failed deletes from the database.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 14}, "id": 28, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by () (increase(error_count{job=\"ews-integrator-data-processing\", namespace=\"$namespace\"}[5m]))", "interval": "", "legendFormat": "General errors", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (topic) (increase(failed_messages_count{job=\"ews-integrator-data-processing\", namespace=\"$namespace\"}[5m]))", "hide": false, "interval": "", "legendFormat": "Failed to produce -- {{topic}}", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by () (increase(kafka_errors{job=\"ews-integrator-data-processing\", namespace=\"$namespace\"}[5m]))", "hide": false, "interval": "", "legendFormat": "<PERSON><PERSON><PERSON> errors", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by () (increase(failed_deletes_count{job=\"ews-integrator-data-processing\", namespace=\"$namespace\"}[5m]))", "hide": false, "interval": "", "legendFormat": "Failed deletes", "range": true, "refId": "D"}], "title": "Errors", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 21}, "id": 22, "panels": [], "title": "Detailed", "type": "row"}, {"datasource": {"type": "prometheus"}, "description": "Consumed deposits from platform.accounting.transactions count by business unit.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 22}, "id": 43, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (BU) (increase(consumed_messages_count{job=\"ews-integrator-data-processing\", topic=\"platform.accounting.transactions\", message_type=\"DEPOSIT\", namespace=\"$namespace\"}[5m])) > 0", "interval": "", "legendFormat": "{{BU}}", "range": true, "refId": "A"}], "title": "Consumed deposits - platform.accounting.transactions", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Consumed withdrawals from platform.accounting.transactions count by business unit.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 22}, "id": 44, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (BU) (increase(consumed_messages_count{job=\"ews-integrator-data-processing\", topic=\"platform.accounting.transactions\", message_type=\"WITHDRAWAL\", namespace=\"$namespace\"}[5m])) > 0", "interval": "", "legendFormat": "{{BU}}", "range": true, "refId": "A"}], "title": "Consumed withdrawals - platform.accounting.transactions", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Processed withdrawals count by business unit.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 28}, "id": 40, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (BU) (increase(processed_messages_count{job=\"ews-integrator-data-processing\", message_type=\"DEPOSIT\", namespace=\"$namespace\"}[5m])) > 0", "interval": "", "legendFormat": "{{BU}}", "range": true, "refId": "A"}], "title": "Processed deposits", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Consumed withdrawals from payment-transfers count by business unit.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 28}, "id": 37, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (BU) (increase(consumed_messages_count{job=\"ews-integrator-data-processing\", topic=\"payment-transfers\", message_type=\"WITHDRAWAL\", namespace=\"$namespace\"}[5m])) > 0", "interval": "", "legendFormat": "{{BU}}", "range": true, "refId": "A"}], "title": "Consumed withdrawals - payment-transfers", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Produced deposits into integrator-payment-transfers topic,  count by business unit.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 35}, "id": 32, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (BU) (increase(produced_messages_count{job=\"ews-integrator-data-processing\", topic=\"integrator-payment-transfers\", message_type=\"DEPOSIT\", namespace=\"$namespace\"}[5m])) > 0", "interval": "", "legendFormat": "{{BU}}", "range": true, "refId": "A"}], "title": "Produced deposits - integrator-payment-transfers", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Processed withdrawals count by business unit.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 35}, "id": 41, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (BU) (increase(processed_messages_count{job=\"ews-integrator-data-processing\", message_type=\"WITHDRAWAL\", namespace=\"$namespace\"}[5m])) > 0", "interval": "", "legendFormat": "{{BU}}", "range": true, "refId": "A"}], "title": "Processed withdrawals", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Failed database deletes, count by business unit.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 42}, "id": 45, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (BU) (increase(failed_deletes_count{job=\"ews-integrator-data-processing\", namespace=\"$namespace\"}[5m])) > 0", "interval": "", "legendFormat": "{{BU}}", "range": true, "refId": "A"}], "title": "Failed deletes", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Produced withdrawals into integrator-payment-transfers topic, count by business unit.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 42}, "id": 33, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (BU) (increase(produced_messages_count{job=\"ews-integrator-data-processing\", topic=\"integrator-payment-transfers\", message_type=\"WITHDRAWAL\", namespace=\"$namespace\"}[5m])) > 0", "interval": "", "legendFormat": "{{BU}}", "range": true, "refId": "A"}], "title": "Produced withdrawals - integrator-payment-transfers", "type": "timeseries"}], "preload": false, "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "ews-integrator-data-processing", "value": "ews-integrator-data-processing"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{}, job)", "includeAll": false, "label": "Job", "name": "job", "options": [], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/ews-integrator-data-processing.*/", "type": "query"}, {"current": {"text": "ews-dev", "value": "ews-dev"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "includeAll": false, "label": "Namespace", "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"isNone": true, "text": "None", "value": ""}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "includeAll": false, "label": "Instance", "name": "instance", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"current": {"isNone": true, "text": "None", "value": ""}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "hide": 2, "includeAll": false, "label": "Pod", "name": "pod", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"isNone": true, "text": "None", "value": ""}, "datasource": {"type": "prometheus"}, "definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "includeAll": false, "label": "Podname", "name": "podname", "options": [], "query": {"query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "integrator-data-processing", "uid": "6314ca53d3934401b9e4", "version": 5, "weekStart": ""}