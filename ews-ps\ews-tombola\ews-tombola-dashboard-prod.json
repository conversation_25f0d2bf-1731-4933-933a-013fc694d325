{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "iteration": 1752139589057, "links": [], "panels": [{"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 35, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "increase(http_server_requests_seconds_count{job=\"$job\", namespace=\"$namespace\", instance=\"$instance\", uri!~\".*actuator.*\"}[$__interval])", "interval": "", "legendFormat": "{{method}} [{{status}}] - {{uri}}", "range": true, "refId": "A"}], "title": "Throughput", "transparent": true, "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 41, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": ["max"], "displayMode": "table", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max by (uri) (histogram_quantile(1,increase(http_server_requests_seconds_bucket{job=\"$job\",namespace=\"$namespace\"}[$__interval])))", "hide": false, "interval": "", "legendFormat": "{{method}} {{uri}}", "range": true, "refId": "A"}], "title": "Latency", "transparent": true, "type": "timeseries"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"color": {}, "custom": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 8}, "hiddenSeries": false, "id": 33, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "max(http_server_requests_seconds_max{job=\"$job\", namespace=\"$namespace\", instance=\"$instance\", uri!~\".*actuator.*\"}) by (uri)", "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Max latency per URI", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:272", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:273", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"color": {}, "custom": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 8}, "hiddenSeries": false, "id": 8, "interval": "20", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "", "bucketAggs": [{"id": "2", "settings": {"interval": "auto"}, "type": "date_histogram"}], "expr": "rate(logback_events_total{job=\"$job\", namespace=\"$namespace\", level=\"error\", instance=\"$instance\"}[5m])", "interval": "", "legendFormat": "", "metrics": [{"id": "1", "type": "count"}], "query": "", "refId": "A", "timeField": "@timestamp"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:507", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:508", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Max duration": "dark-purple", "p90.0 duration": "dark-green", "p95.0 duration": "semi-dark-yellow", "p96.0 duration": "semi-dark-blue", "p97.0 duration": "dark-orange", "p98.0 duration": "light-red", "p99.0 duration": "dark-red"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "reqps"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 14}, "hiddenSeries": false, "id": 6, "interval": "30", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "", "bucketAggs": [{"id": "2", "settings": {"interval": "auto"}, "type": "date_histogram"}], "expr": "rate(http_server_requests_seconds_count{job=\"$job\", namespace=\"$namespace\", instance=\"$instance\", uri!~\".*actuator.*\"}[5m])", "interval": "", "legendFormat": "{{method}} [{{status}}] - {{uri}}", "metrics": [{"id": "1", "type": "count"}], "query": "", "refId": "A", "timeField": "@timestamp"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests per second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:460", "format": "reqps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:461", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 14}, "hiddenSeries": false, "id": 18, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(jvm_threads_states_threads{job=\"$job\", namespace=\"$namespace\", instance=\"$instance\", state=\"waiting\", service=\"ews-tombola\"})", "interval": "", "legendFormat": "Waiting", "refId": "A"}, {"expr": "sum(jvm_threads_states_threads{job=\"$job\", namespace=\"$namespace\", instance=\"$instance\", state=\"timed-waiting\", service=\"ews-tombola\"})", "hide": false, "interval": "", "legendFormat": "Timed-waiting", "refId": "C"}, {"expr": "sum(jvm_threads_states_threads{job=\"$job\", namespace=\"$namespace\", instance=\"$instance\", state=\"runnable\", service=\"ews-tombola\"})", "hide": false, "interval": "", "legendFormat": "Runnable", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "All thread count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1431", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1432", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": null, "description": "Displaying the CPU usage of the ews-tombola containers as a percentage of their allocated CPU resources. The query calculates the average CPU usage over the last 5 minutes and compares it to the CPU allocation defined for each container. Values above 100% indicate the container is using more CPU than what has been allocated to it.", "fieldConfig": {"defaults": {"color": {"fixedColor": "semi-dark-green", "mode": "fixed"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "byFrameRefID", "options": "B"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.lineWidth", "value": 2}, {"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"matcher": {"id": "byFrameRefID", "options": "C"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.lineWidth", "value": 2}, {"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}, "id": 37, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{namespace=\"$namespace\", pod=~\"^ews-tombola.*\", container!=\"\"}) by (container)", "hide": false, "interval": "", "legendFormat": "{{container}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(kube_pod_container_resource_requests{job=\"kube-state-metrics\", namespace=\"$namespace\", pod=~\"^ews-tombola.*\", resource=\"cpu\"})", "hide": false, "instant": false, "interval": "", "legendFormat": "requests", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(kube_pod_container_resource_limits{job=\"kube-state-metrics\", namespace=\"$namespace\", pod=~\"^ews-tombola.*\", resource=\"cpu\"})", "hide": false, "instant": false, "interval": "", "legendFormat": "limits", "range": true, "refId": "C"}], "title": "CPU", "transparent": true, "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "byFrameRefID", "options": "B"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.lineWidth", "value": 2}, {"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "byFrameRefID", "options": "C"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.lineWidth", "value": 2}, {"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "id": 39, "interval": "1m", "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(container_memory_working_set_bytes{job=\"kubelet\", metrics_path=\"/metrics/cadvisor\", namespace=\"$namespace\", pod=~\"^ews-tombola.*\", container!=\"\", image!=\"\"}) by (container)", "hide": false, "interval": "", "legendFormat": "{{container}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(kube_pod_container_resource_requests{job=\"kube-state-metrics\", namespace=\"$namespace\", pod=~\"^ews-tombola.*\", resource=\"memory\"})", "hide": false, "interval": "", "legendFormat": "requests", "range": true, "refId": "B"}], "title": "Memory", "transparent": true, "type": "timeseries"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 28}, "hiddenSeries": false, "id": 29, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "max(hikaricp_connections_creation_seconds_max{job=\"$job\", namespace=\"$namespace\", instance=\"$instance\", service=\"ews-tombola\"})", "interval": "", "legendFormat": "Max time of DB connection creation", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "DB Connection creation time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "decgbytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 12, "x": 12, "y": 28}, "hiddenSeries": false, "id": 20, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(jvm_buffer_memory_used_bytes{job=\"$job\", namespace=\"$namespace\", instance=\"$instance\", service=\"ews-tombola\"})/1073741824", "interval": "", "legendFormat": "I/O Memory Buffers", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "I/O Memory Buffers", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:247", "format": "decgbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:248", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"color": {}, "custom": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "decgbytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 33}, "hiddenSeries": false, "id": 26, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(jvm_memory_committed_bytes{job=\"$job\", namespace=\"$namespace\", instance=\"$instance\", service=\"ews-tombola\", area=\"heap\"})/1073741824", "interval": "", "legendFormat": "<PERSON><PERSON> commited", "refId": "A"}, {"expr": "sum(jvm_memory_used_bytes{job=\"$job\", namespace=\"$namespace\", instance=\"$instance\", service=\"ews-tombola\", area=\"heap\"})/1073741824", "hide": false, "interval": "", "legendFormat": "Heap used", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Heap USED Memory Disection", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:88", "format": "decgbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:89", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 12, "x": 12, "y": 33}, "hiddenSeries": false, "id": 31, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(hikaricp_connections_active{job=\"$job\", namespace=\"$namespace\", instance=\"$instance\", service=\"ews-tombola\"})", "interval": "", "legendFormat": "Active connections", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "DB Active connections", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"color": {}, "custom": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "decgbytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 39}, "hiddenSeries": false, "id": 27, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(jvm_memory_committed_bytes{job=\"$job\", namespace=\"$namespace\", instance=\"$instance\", service=\"ews-tombola\", area=\"nonheap\"})/1073741824", "interval": "", "legendFormat": "Non Heap commited", "refId": "A"}, {"expr": "sum(jvm_memory_used_bytes{job=\"$job\", namespace=\"$namespace\", instance=\"$instance\", service=\"ews-tombola\", area=\"nonheap\"})/1073741824", "hide": false, "interval": "", "legendFormat": "Non Heap used", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Non-Heap USED Memory Disection", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:168", "format": "decgbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:169", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "ews-tombola", "value": "ews-tombola"}, "datasource": null, "definition": "label_values(jvm_classes_loaded_classes{}, job)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Job", "multi": false, "name": "job", "options": [], "query": {"query": "label_values(jvm_classes_loaded_classes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/ews-tombola.*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-sof-prod", "value": "ews-sof-prod"}, "datasource": null, "definition": "label_values(jvm_classes_loaded_classes{job=\"$job\"}, namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(jvm_classes_loaded_classes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": "************:10210", "value": "************:10210"}, "datasource": null, "definition": "label_values(jvm_classes_loaded_classes{job=\"$job\", namespace=\"$namespace\"},instance)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Instance", "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(jvm_classes_loaded_classes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {"hidden": false, "refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"], "type": "timepicker"}, "timezone": "browser", "title": "ews-tombola", "uid": "IAl7ER-asdVz", "version": 3}