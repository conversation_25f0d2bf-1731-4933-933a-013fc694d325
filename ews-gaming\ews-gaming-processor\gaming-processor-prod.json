{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 291, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "percentunit"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 0}, "hiddenSeries": false, "id": 16, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "system_cpu_usage{service=\"ews-gaming-processor\"}", "instant": false, "interval": "", "legendFormat": "instance: {{instance}}, pod: {{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1251", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1252", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 0}, "hiddenSeries": false, "id": 18, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(jvm_threads_states_threads{state=\"waiting\", service=\"ews-gaming-processor\"})", "interval": "", "legendFormat": "Waiting", "refId": "A"}, {"expr": "sum(jvm_threads_states_threads{state=\"timed-waiting\", service=\"ews-gaming-processor\"})", "hide": false, "interval": "", "legendFormat": "Timed-waiting", "refId": "C"}, {"expr": "sum(jvm_threads_states_threads{state=\"runnable\", service=\"ews-gaming-processor\"})", "hide": false, "interval": "", "legendFormat": "Runnable", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "All thread count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1431", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1432", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 6}, "hiddenSeries": false, "id": 29, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "consumerGroupMetrics{job=\"gam-redis-metrics\", streamName=~\"gaming-game-session\", key=\"delay\"}", "interval": "", "legendFormat": "Game session delay", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Game session - <PERSON><PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1173", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1174", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 6}, "hiddenSeries": false, "id": 30, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "consumerGroupMetrics{job=\"gam-redis-metrics\", streamName=~\"gaming-audit-counter\", key=\"delay\"}", "interval": "", "legendFormat": "Audit counter delay", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Audit counter - Delay", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1173", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1174", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 6}, "hiddenSeries": false, "id": 31, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "consumerGroupMetrics{job=\"gam-redis-metrics\", streamName=~\"gaming-freespins-bonus\", key=\"delay\"}", "interval": "", "legendFormat": "Freespins bonus delay", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Freespins bonus - <PERSON>ay", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1173", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1174", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "decgbytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 11}, "hiddenSeries": false, "id": 20, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(jvm_buffer_memory_used_bytes{service=\"ews-gaming-processor\"})/1073741824", "interval": "", "legendFormat": "I/O Memory Buffers", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "I/O Memory Buffers", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:247", "format": "decgbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:248", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 11}, "hiddenSeries": false, "id": 22, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(jvm_buffer_count_buffers{service=\"ews-gaming-processor\"})", "interval": "", "legendFormat": "<PERSON>/O Buffers", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON>/O Buffers", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:423", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:424", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 11}, "hiddenSeries": false, "id": 14, "interval": "30", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(jvm_gc_pause_seconds_max{service=\"ews-gaming-processor\"}[1m]))", "interval": "", "legendFormat": "GC pause time", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "GC pause time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1074", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1075", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "light-green", "value": null}, {"color": "dark-orange", "value": 80}, {"color": "dark-red", "value": 90}]}, "unit": "decgbytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Heap used"}, "properties": [{"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "semi-dark-green", "value": null}]}}]}]}, "gridPos": {"h": 5, "w": 10, "x": 0, "y": 16}, "id": 24, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\", area=\"heap\"})/1073741824", "interval": "", "legendFormat": "Heap used", "refId": "A"}, {"expr": "sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\", id=\"G1 Eden Space\"})/1073741824", "hide": false, "interval": "", "legendFormat": "G1 Eden Space", "refId": "B"}, {"expr": "sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\", id=\"G1 Old Gen\"})/1073741824", "hide": false, "interval": "", "legendFormat": "G1 Old Gen", "refId": "C"}, {"expr": "sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\", id=\"G1 Survivor Space\"})/1073741824", "hide": false, "interval": "", "legendFormat": "G1 Survivor Space", "refId": "D"}], "title": "Heap COMITTED Memory Disection", "transparent": true, "type": "gauge"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "light-green", "value": null}, {"color": "dark-orange", "value": 80}, {"color": "dark-red", "value": 90}]}, "unit": "decgbytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Non Heap used"}, "properties": [{"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "semi-dark-green", "value": null}]}}]}]}, "gridPos": {"h": 5, "w": 14, "x": 10, "y": 16}, "id": 25, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\", area=\"nonheap\"})/1073741824", "interval": "", "legendFormat": "Non Heap used", "refId": "A"}, {"expr": "sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\", id=\"Metaspace\"})/1073741824", "hide": false, "interval": "", "legendFormat": "Metaspace", "refId": "B"}, {"expr": "sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\", id=\"CodeHeap 'non-nmethods'\"})/1073741824", "hide": false, "interval": "", "legendFormat": "Non-nmethods", "refId": "C"}, {"expr": "sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\",id=\"CodeHeap 'non-profiled nmethods'\"})/1073741824", "hide": false, "interval": "", "legendFormat": "Non-profiled nmethods", "refId": "D"}, {"expr": "sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\",id=\"CodeHeap 'profiled nmethods'\"})/1073741824", "hide": false, "interval": "", "legendFormat": "Profiled nmethods", "refId": "E"}, {"expr": "sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\", id=\"Compressed Class Space\"})/1073741824", "hide": false, "interval": "", "legendFormat": "Profiled nmethods", "refId": "F"}], "title": "Non-Heap COMITTED Memory Disection", "transparent": true, "type": "gauge"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "light-green", "value": null}, {"color": "dark-orange", "value": 80}, {"color": "dark-red", "value": 90}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 5, "w": 10, "x": 0, "y": 21}, "id": 26, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(jvm_memory_used_bytes{service=\"ews-gaming-processor\", area=\"heap\"})/sum(jvm_memory_committed_bytes{service=\"ews-gaming-accounting\", area=\"heap\"})", "interval": "", "legendFormat": "Heap used", "refId": "A"}, {"expr": "sum(jvm_memory_used_bytes{service=\"ews-gaming-processor\", id=\"G1 Eden Space\"})/sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\", id=\"G1 Eden Space\"})", "hide": false, "interval": "", "legendFormat": "G1 Eden Space", "refId": "B"}, {"expr": "sum(jvm_memory_used_bytes{service=\"ews-gaming-processor\", id=\"G1 Old Gen\"})/sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\", id=\"G1 Old Gen\"})", "hide": false, "interval": "", "legendFormat": "G1 Old Gen", "refId": "C"}, {"expr": "sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\", id=\"G1 Survivor Space\"})/sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\", id=\"G1 Survivor Space\"})", "hide": false, "interval": "", "legendFormat": "G1 Survivor Space", "refId": "D"}], "title": "Heap USED Memory Disection", "transparent": true, "type": "gauge"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "light-green", "value": null}, {"color": "dark-orange", "value": 80}, {"color": "dark-red", "value": 90}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 5, "w": 14, "x": 10, "y": 21}, "id": 27, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum(jvm_memory_used_bytes{service=\"ews-gaming-processor\", area=\"nonheap\"})/sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\", area=\"nonheap\"})", "interval": "", "legendFormat": "Non Heap used", "refId": "A"}, {"expr": "sum(jvm_memory_used_bytes{service=\"ews-gaming-processor\", id=\"Metaspace\"})/sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\", id=\"Metaspace\"})", "hide": false, "interval": "", "legendFormat": "Metaspace", "refId": "B"}, {"expr": "sum(jvm_memory_used_bytes{service=\"ews-gaming-processor\", id=\"CodeHeap 'non-nmethods'\"})/sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\", id=\"CodeHeap 'non-nmethods'\"})", "hide": false, "interval": "", "legendFormat": "Non-nmethods", "refId": "C"}, {"expr": "sum(jvm_memory_used_bytes{service=\"ews-gaming-processor\",id=\"CodeHeap 'non-profiled nmethods'\"})/sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\",id=\"CodeHeap 'non-profiled nmethods'\"})", "hide": false, "interval": "", "legendFormat": "Non-profiled nmethods", "refId": "D"}, {"expr": "sum(jvm_memory_used_bytes{service=\"ews-gaming-processor\",id=\"CodeHeap 'profiled nmethods'\"})/sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\",id=\"CodeHeap 'profiled nmethods'\"})", "hide": false, "interval": "", "legendFormat": "Profiled nmethods", "refId": "E"}, {"expr": "sum(jvm_memory_used_bytes{service=\"ews-gaming-processor\", id=\"Compressed Class Space\"})/sum(jvm_memory_committed_bytes{service=\"ews-gaming-processor\", id=\"Compressed Class Space\"})", "hide": false, "interval": "", "legendFormat": "Profiled nmethods", "refId": "F"}], "title": "Non-Heap USED Memory Disection", "transparent": true, "type": "gauge"}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"hidden": true, "refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"], "type": "timepicker"}, "timezone": "browser", "title": "ews-gaming-processor", "uid": "vi-Wo3pVk", "version": 7}