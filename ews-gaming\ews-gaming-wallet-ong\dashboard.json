{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 152, "links": [], "panels": [{"aliasColors": {"Max duration": "dark-purple", "p90.0 duration": "dark-green", "p95.0 duration": "semi-dark-yellow", "p96.0 duration": "semi-dark-blue", "p97.0 duration": "dark-orange", "p98.0 duration": "light-red", "p99.0 duration": "dark-red"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Elasticsearch-pl2-ews30-gaming", "fieldConfig": {"defaults": {"custom": {}, "unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 0}, "hiddenSeries": false, "id": 6, "interval": "30", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "", "bucketAggs": [{"id": "2", "settings": {"interval": "auto"}, "type": "date_histogram"}], "metrics": [{"field": "duration", "id": "1", "type": "max"}], "query": "kubernetes_labels.io.kompose.service:\"ews-gaming-wallet-ong\"", "refId": "A", "timeField": "@timestamp"}, {"alias": "", "bucketAggs": [{"id": "2", "settings": {"interval": "auto"}, "type": "date_histogram"}], "hide": false, "metrics": [{"field": "duration", "id": "1", "settings": {"percents": ["90", "95", "96", "97", "98", "99"]}, "type": "percentiles"}], "query": "kubernetes_labels.io.kompose.service:\"ews-gaming-wallet-ong\" ", "refId": "B", "timeField": "@timestamp"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Max Request Duration Percentile", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:460", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:461", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "percentunit"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 0}, "hiddenSeries": false, "id": 16, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "system_cpu_usage{service=\"ews-gaming-wallet-ong\"}", "instant": false, "interval": "", "legendFormat": "instance: {{instance}}, pod: {{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1251", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1252", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 0}, "hiddenSeries": false, "id": 31, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(lettuce_command_completion_seconds_count{container=\"ews-gaming-wallet-ong\"}[1m]))", "interval": "", "legendFormat": "{{command}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON>is - Count of calls - WALLET - ONG", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:804", "format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:805", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "Elasticsearch-pl2-ews30-gaming", "fieldConfig": {"defaults": {"color": {}, "custom": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 0}, "hiddenSeries": false, "id": 8, "interval": "20", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "", "bucketAggs": [{"id": "2", "settings": {"interval": "auto"}, "type": "date_histogram"}], "metrics": [{"id": "1", "type": "count"}], "query": "kubernetes_labels.io.kompose.service:\"ews-gaming-wallet-ong\" AND *Exception* AND NOT \"errorCode=ACC_2013\" AND NOT \"errorCode=ACC_2023\"", "refId": "A", "timeField": "@timestamp"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:507", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:508", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 5}, "hiddenSeries": false, "id": 10, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "max(lettuce_command_completion_seconds_max{container=\"ews-gaming-wallet-ong\"})by(command)", "interval": "", "legendFormat": "{{command}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Redis  - Max duration - WALLET - ONG", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:804", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:805", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 5}, "hiddenSeries": false, "id": 29, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "max(rate(lettuce_command_completion_seconds_sum{container=\"ews-gaming-wallet-ong\"}[1m])/rate(lettuce_command_completion_seconds_count{container=\"ews-gaming-wallet-ong\"}[1m]))by(command)", "interval": "", "legendFormat": "{{command}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": " Redis - Average duration of calls - WALLET - ONG", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:804", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:805", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 5}, "hiddenSeries": false, "id": 30, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(lettuce_command_completion_seconds_count{container=\"ews-gaming-wallet-ong\"}[1m]))by(command)", "interval": "", "legendFormat": "{{command}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON>is - Count of calls - WALLET - ONG", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:804", "format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:805", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 5}, "hiddenSeries": false, "id": 32, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(redis_commands_total{service=\"redis-gam-all\"}[1m]))by(cmd)", "interval": "", "legendFormat": "{{command}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Redis - Count of calls - GAMING", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:804", "format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:805", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"color": {}, "custom": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "decgbytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 10}, "hiddenSeries": false, "id": 26, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(jvm_memory_committed_bytes{service=\"ews-gaming-wallet-ong\", area=\"heap\"})/1073741824", "interval": "", "legendFormat": "<PERSON><PERSON> commited", "refId": "A"}, {"expr": "sum(jvm_memory_used_bytes{service=\"ews-gaming-wallet-ong\", area=\"heap\"})/1073741824", "hide": false, "interval": "", "legendFormat": "Heap used", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Heap USED Memory Disection", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3845", "format": "decgbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3846", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 10}, "hiddenSeries": false, "id": 28, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "max(hikaricp_connections_creation_seconds_max{service=\"ews-gaming-wallet-ong\"})", "interval": "", "legendFormat": "Max time of DB connection creation", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "DB Connection creation time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:804", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:805", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 6, "x": 12, "y": 10}, "hiddenSeries": false, "id": 18, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(jvm_threads_states_threads{state=\"waiting\", service=\"ews-gaming-wallet-ong\"})", "interval": "", "legendFormat": "Waiting", "refId": "A"}, {"expr": "sum(jvm_threads_states_threads{state=\"timed-waiting\", service=\"ews-gaming-wallet-ong\"})", "hide": false, "interval": "", "legendFormat": "Timed-waiting", "refId": "C"}, {"expr": "sum(jvm_threads_states_threads{state=\"runnable\", service=\"ews-gaming-wallet-ong\"})", "hide": false, "interval": "", "legendFormat": "Runnable", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "All thread count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1431", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1432", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 6, "x": 18, "y": 10}, "hiddenSeries": false, "id": 14, "interval": "30", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(jvm_gc_pause_seconds_max{service=\"ews-gaming-wallet-ong\"}[1m]))", "interval": "", "legendFormat": "gc pause time seconds", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "GC pause time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1074", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1075", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"color": {}, "custom": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "decgbytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 15}, "hiddenSeries": false, "id": 27, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(jvm_memory_committed_bytes{service=\"ews-gaming-wallet-ong\", area=\"nonheap\"})/1073741824", "interval": "", "legendFormat": "Non Heap commited", "refId": "A"}, {"expr": "sum(jvm_memory_used_bytes{service=\"ews-gaming-wallet-ong\", area=\"nonheap\"})/1073741824", "hide": false, "interval": "", "legendFormat": "Non Heap used", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Non-Heap USED Memory Disection", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3925", "format": "decgbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3926", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 6, "x": 6, "y": 15}, "hiddenSeries": false, "id": 12, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(hikaricp_connections_active{service=\"ews-gaming-wallet-ong\"})", "interval": "", "legendFormat": "Active connections", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "DB Active connections", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:868", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:869", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "decgbytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 16}, "hiddenSeries": false, "id": 20, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(jvm_buffer_memory_used_bytes{service=\"ews-gaming-wallet-ong\"})/1073741824", "interval": "", "legendFormat": "I/O Memory Buffers", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "I/O Memory Buffers", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:247", "format": "decgbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:248", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 16}, "hiddenSeries": false, "id": 22, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(jvm_buffer_count_buffers{service=\"ews-gaming-wallet-ong\"})", "interval": "", "legendFormat": "<PERSON>/O Buffers", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON>/O Buffers", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:423", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:424", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"hidden": false, "refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"], "type": "timepicker"}, "timezone": "browser", "title": "ews-gaming-wallet-ong", "uid": "bp7arpcVz", "version": 9}