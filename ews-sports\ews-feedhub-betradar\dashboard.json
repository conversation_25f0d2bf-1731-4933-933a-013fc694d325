{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 4777, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 60, "panels": [], "title": "ScyllaDB", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 61, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(scylladb_message_size_bytes_sum{job=~\"$job\", namespace=~\"$namespace\"}[5m]) \r\n/ \r\nrate(scylladb_message_size_bytes_count{job=~\"$job\", namespace=~\"$namespace\"}[5m])", "hide": false, "interval": "", "legendFormat": "ScyllaDB message avg msg size", "range": true, "refId": "A"}], "title": "Avg. Message Size (kB)", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 8, "w": 23, "x": 0, "y": 9}, "id": 62, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(execution_processed_count_total{job=~\"$job\", namespace=~\"$namespace\"}[5m])", "hide": false, "interval": "", "legendFormat": "{{controller}} / {{action}}", "range": true, "refId": "A"}], "title": "Processed Count per Controller (ops)", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 23, "x": 0, "y": 17}, "id": 63, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(execution_duration_seconds_sum{job=~\"$job\", namespace=~\"$namespace\"}[5m])\r\n/\r\nrate(execution_duration_seconds_count{job=~\"$job\", namespace=~\"$namespace\"}[5m])", "hide": false, "interval": "", "legendFormat": "{{controller}} / {{action}}", "range": true, "refId": "A"}], "title": "Average Execution Duration (per controller/action)", "transformations": [{"id": "sortBy", "options": {"fields": {}, "sort": [{"field": "Time"}]}}], "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 23, "x": 0, "y": 25}, "id": 64, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "100 * (\r\n  rate(execution_failed_total{job=~\"$job\", namespace=~\"$namespace\"}[5m])\r\n  /\r\n  rate(execution_processed_count_total{job=~\"$job\", namespace=~\"$namespace\"}[5m])\r\n)", "hide": false, "interval": "", "legendFormat": "{{controller}} / {{action}}", "range": true, "refId": "A"}], "title": "Failure Rate (%)", "transformations": [{"id": "sortBy", "options": {"fields": {}, "sort": [{"field": "Time"}]}}], "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 23, "x": 0, "y": 33}, "id": 65, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(execution_duration_seconds_sum{action=~\"GetStateByFixtureAsync|PreserveStateAggregateAsync\",job=~\"$job\", namespace=~\"$namespace\"}[5m]) \r\n/ \r\nrate(execution_duration_seconds_count{action=~\"GetStateByFixtureAsync|PreserveStateAggregateAsync\",job=~\"$job\", namespace=~\"$namespace\"}[5m])", "hide": false, "interval": "", "legendFormat": "{{action}}", "range": true, "refId": "A"}], "title": "Read and Write operations average time", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["GetStateByFixtureAsync"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 8, "w": 23, "x": 0, "y": 41}, "id": 66, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(execution_processed_count_total{action=~\"GetStateByFixtureAsync|PreserveStateAggregateAsync\",job=~\"$job\", namespace=~\"$namespace\"}[5m])", "hide": false, "interval": "", "legendFormat": "{{action}}", "range": true, "refId": "A"}], "title": "Read and Write operations per second", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 49}, "id": 59, "panels": [{"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 9}, "id": 58, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "max", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "avg(rate(execution_duration_seconds_sum{controller=~\"UnresolvedEventInfoCache\", job=~\"$job\", namespace=~\"$namespace\"}[5m])/rate(execution_duration_seconds_count{controller=~\"UnresolvedEventInfoCache\", job=~\"$job\", namespace=~\"$namespace\"}[5m])) by (action, controller)", "interval": "", "legendFormat": "{{controller}}.{{action}}", "refId": "A"}], "title": "Redis Execution Durations", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 17}, "id": 56, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true, "width": 450}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "avg(rate(execution_duration_seconds_sum{action=~\"GetMappingInfo\", job=~\"$job\", namespace=~\"$namespace\"}[5m])/rate(execution_duration_seconds_count{action=~\"GetMappingInfo\", job=~\"$job\", namespace=~\"$namespace\"}[5m])) by (action, controller)", "interval": "", "legendFormat": "{{controller}}.{{action}}", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "avg(rate(execution_duration_seconds_sum{action=~\"GetPlayersMappings\", job=~\"$job\", namespace=~\"$namespace\"}[5m])/rate(execution_duration_seconds_count{action=~\"GetPlayersMappings\", job=~\"$job\", namespace=~\"$namespace\"}[5m])) by (action, controller)", "hide": false, "interval": "", "legendFormat": "{{controller}}.{{action}}", "refId": "B"}], "title": "Mapping Block Execution Durations", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 25}, "id": 54, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(feedhub_message_counter[5m])", "interval": "", "legendFormat": "{{feed}}_{{messageType}}", "refId": "A"}], "title": "Message counter", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 25}, "id": 36, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(block_size{job=~\"$job\", namespace=~\"$namespace\"}) by (block)", "interval": "", "legendFormat": "{{block}}", "refId": "A"}], "title": "Block Size", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 33}, "id": 34, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "avg(rate(execution_duration_seconds_sum{action=~\"BetRadar\", job=~\"$job\", namespace=~\"$namespace\"}[5m])/rate(execution_duration_seconds_count{action=~\"BetRadar\", job=~\"$job\", namespace=~\"$namespace\"}[5m])) by (action, controller)", "interval": "", "legendFormat": " {{controller}}.{{action}}", "refId": "A"}], "title": "Execution Durations", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 33}, "id": 40, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "avg(rate(feedhub_provider_kafka_delay_seconds_sum{job=~\"$job\", namespace=~\"$namespace\"}[5m])/rate(feedhub_provider_kafka_delay_seconds_count{job=~\"$job\", namespace=~\"$namespace\"}[5m])) by (feed)\r", "interval": "", "legendFormat": "Time in Kafka in Seconds - {{feed}}", "refId": "A"}], "title": "Service Time", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 41}, "id": 38, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "avg(rate(feedhub_message_processing_time_sum{job=~\"$job\", namespace=~\"$namespace\"}[5m])/rate(feedhub_message_processing_time_count{job=~\"$job\", namespace=~\"$namespace\"}[5m])) by (feed)", "interval": "", "legendFormat": "{{feed}}", "refId": "A"}], "title": "Execution Durations", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 7, "x": 17, "y": 41}, "id": 44, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(kafka_errors{job=\"ews-feedhub-betradar\", namespace=\"$namespace\"}[1m]) * 60", "interval": "", "legendFormat": "{{action}}", "refId": "A"}], "title": "Kafka Events", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 17, "x": 0, "y": 49}, "id": 42, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(kafka_consumergroup_lag{consumergroup=~\"BetRadarConsumer_DEV\",topic=~\"feed-updates-betradar\"}) by (consumergroup)", "interval": "", "legendFormat": "{{consumergroup}}", "refId": "A"}], "title": "Kafka Lag", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 57}, "id": 46, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(pipeline_errors{job=~\"$job\", namespace=~\"$namespace\"}[1m]) * 60", "interval": "", "legendFormat": "{{error}}", "refId": "A"}], "title": "Pipeline Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 57}, "id": 48, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(producer_errors{job=~\"$job\", namespace=~\"$namespace\"}[1m]) * 60", "interval": "", "legendFormat": "{{error}}", "refId": "A"}], "title": "Producer Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 65}, "id": 50, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(redis_errors{job=~\"$job\", namespace=~\"$namespace\"}[1m]) * 60", "interval": "", "legendFormat": "{{error}}", "refId": "A"}], "title": "<PERSON><PERSON>", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 65}, "id": 52, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(kafka_errors{job=~\"$job\", namespace=~\"$namespace\"}[1m]) * 60", "interval": "", "legendFormat": "{{action}}", "refId": "A"}], "title": "<PERSON><PERSON><PERSON>", "type": "timeseries"}], "title": "Row title", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 50}, "id": 22, "panels": [{"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 41}, "id": 2, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(container_cpu_usage_seconds_total{pod=\"$podname\"}[5m]))", "interval": "", "legendFormat": "cpu usage", "refId": "A"}], "title": "Number of cores used", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 41}, "id": 24, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_private_memory_bytes{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_private_memory_bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 49}, "id": 14, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "http_request_duration_seconds_sum{instance=\"$instance\", uri!~\".*actuator.*\"}", "interval": "", "legendFormat": "{{action}} [{{code}}] {{controller}}", "refId": "A"}], "title": "http_request_duration_seconds_sum", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 59}, "id": 26, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_open_handles{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_open_handles", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 59}, "id": 28, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_num_threads{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_num_threads", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 67}, "id": 30, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_start_time_seconds{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_start_time_seconds", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 67}, "id": 32, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_working_set_bytes{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_working_set_bytes", "type": "timeseries"}], "title": "General", "type": "row"}], "preload": false, "refresh": false, "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "ews-feedhub-betradar", "value": "ews-feedhub-betradar"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{}, job)", "includeAll": false, "label": "Job", "name": "job", "options": [], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/ews-feedhub-betradar.*/", "type": "query"}, {"current": {"text": "ews-dev", "value": "ews-dev"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "includeAll": false, "label": "Namespace", "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "172.17.33.233:80", "value": "172.17.33.233:80"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "includeAll": false, "label": "Instance", "name": "instance", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"current": {"text": "ews-feedhub-betradar-86b5c5d45c-q8w64", "value": "ews-feedhub-betradar-86b5c5d45c-q8w64"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "hide": 2, "includeAll": false, "label": "Pod", "name": "pod", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "ews-feedhub-betradar-86b5c5d45c-q8w64", "value": "ews-feedhub-betradar-86b5c5d45c-q8w64"}, "datasource": {"type": "prometheus"}, "definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "includeAll": false, "label": "Podname", "name": "podname", "options": [], "query": {"query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "feedhub-betradar", "uid": "9ece48e202784708b12", "version": 1, "weekStart": ""}