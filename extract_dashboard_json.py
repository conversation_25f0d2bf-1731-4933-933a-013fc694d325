#!/usr/bin/env python3
"""
Script to extract JSON content from Grafana dashboard ConfigMap YAML files
and create separate JSON files with "-prod" suffix.
"""

import os
import yaml
import json
import argparse
from pathlib import Path


def extract_json_from_yaml(yaml_file_path):
    """
    Extract JSON content from a YAML ConfigMap file.
    
    Args:
        yaml_file_path (Path): Path to the YAML file
        
    Returns:
        dict: Dictionary with key names and their JSON content
    """
    try:
        with open(yaml_file_path, 'r', encoding='utf-8') as file:
            yaml_content = yaml.safe_load(file)
        
        # Check if it's a ConfigMap with data section
        if (yaml_content and 
            yaml_content.get('kind') == 'ConfigMap' and 
            'data' in yaml_content):
            
            data_section = yaml_content['data']
            extracted_json = {}
            
            for key, value in data_section.items():
                # Check if the key ends with .json and value looks like JSON
                if key.endswith('.json') and isinstance(value, str):
                    try:
                        # Try to parse the value as JSON to validate it
                        json_content = json.loads(value)
                        extracted_json[key] = json_content
                        print(f"  Found JSON data for key: {key}")
                    except json.JSONDecodeError as e:
                        print(f"  Warning: Could not parse JSON for key '{key}' in {yaml_file_path}: {e}")
                        continue
            
            return extracted_json
        else:
            print(f"  Skipping {yaml_file_path}: Not a ConfigMap or no data section")
            return {}
            
    except yaml.YAMLError as e:
        print(f"  Error parsing YAML file {yaml_file_path}: {e}")
        return {}
    except Exception as e:
        print(f"  Error processing file {yaml_file_path}: {e}")
        return {}


def create_json_files(yaml_file_path, extracted_json):
    """
    Create JSON files from extracted content.
    
    Args:
        yaml_file_path (Path): Path to the source YAML file
        extracted_json (dict): Dictionary with key names and JSON content
    """
    yaml_dir = yaml_file_path.parent
    
    for key, json_content in extracted_json.items():
        # Remove .json extension and add -prod.json
        base_name = key.replace('.json', '')
        new_filename = f"{base_name}-prod.json"
        json_file_path = yaml_dir / new_filename
        
        try:
            with open(json_file_path, 'w', encoding='utf-8') as json_file:
                json.dump(json_content, json_file, indent=2, ensure_ascii=False)
            
            print(f"  Created: {json_file_path}")
            
        except Exception as e:
            print(f"  Error creating JSON file {json_file_path}: {e}")


def process_directory(directory_path, dry_run=False):
    """
    Process all YAML files in the directory recursively.
    
    Args:
        directory_path (Path): Path to the directory to process
        dry_run (bool): If True, only show what would be done without creating files
    """
    directory_path = Path(directory_path)
    
    if not directory_path.exists():
        print(f"Error: Directory {directory_path} does not exist")
        return
    
    print(f"Processing directory: {directory_path}")
    print(f"Dry run mode: {dry_run}")
    print("-" * 50)
    
    yaml_files_found = 0
    json_files_created = 0
    
    # Find all YAML files recursively
    for yaml_file in directory_path.rglob("*.yaml"):
        # Skip kustomization.yaml files as they typically don't contain dashboard JSON
        if yaml_file.name == "kustomization.yaml":
            continue
            
        yaml_files_found += 1
        print(f"\nProcessing: {yaml_file}")
        
        extracted_json = extract_json_from_yaml(yaml_file)
        
        if extracted_json:
            if not dry_run:
                create_json_files(yaml_file, extracted_json)
            else:
                yaml_dir = yaml_file.parent
                for key in extracted_json.keys():
                    base_name = key.replace('.json', '')
                    new_filename = f"{base_name}-prod.json"
                    json_file_path = yaml_dir / new_filename
                    print(f"  Would create: {json_file_path}")
            
            json_files_created += len(extracted_json)
        else:
            print(f"  No JSON data found in {yaml_file}")
    
    print("\n" + "=" * 50)
    print(f"Summary:")
    print(f"  YAML files processed: {yaml_files_found}")
    print(f"  JSON files {'would be created' if dry_run else 'created'}: {json_files_created}")


def main():
    parser = argparse.ArgumentParser(
        description="Extract JSON content from Grafana dashboard ConfigMap YAML files"
    )
    parser.add_argument(
        "directory",
        help="Directory to process (will search recursively for YAML files)"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without actually creating files"
    )
    
    args = parser.parse_args()
    
    process_directory(args.directory, args.dry_run)


if __name__ == "__main__":
    main()
