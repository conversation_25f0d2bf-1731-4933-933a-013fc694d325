{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 656, "iteration": 1684243720825, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 22, "panels": [], "title": "General", "type": "row"}, {"aliasColors": {}, "dashLength": 10, "fieldConfig": {"defaults": {"custom": {}, "unit": "s"}, "overrides": []}, "fill": 1, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 43}, "id": 39, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "pointradius": 2, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "targets": [{"expr": "rate(rake_stream_worker_processing_all_threads_sum{pod=\"$podname\"}[10m])/rate(rake_stream_worker_processing_all_threads_count{pod=\"$podname\"}[10m])", "legendFormat": "{{service}}  {{pod}}", "interval": "", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Rake stream event processing", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1059", "format": "s", "label": "Time to process all events", "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1060", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}, "bars": false, "dashes": false, "fillGradient": 0, "hiddenSeries": false, "percentage": false, "points": false, "stack": false, "steppedLine": false, "timeFrom": null, "timeShift": null, "datasource": null}, {"aliasColors": {}, "dashLength": 10, "fieldConfig": {"defaults": {"custom": {}, "color": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "s"}, "overrides": []}, "fill": 1, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 34}, "id": 38, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "pointradius": 2, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "targets": [{"expr": "rate(crm_stream_worker_processing_all_threads_sum{pod=\"$podname\"}[10m])/rate(crm_stream_worker_processing_all_threads_count{pod=\"$podname\"}[10m])", "legendFormat": "{{service}}  {{pod}}", "interval": "", "refId": "A", "valueHandler": "Number Threshold", "displayType": "Regular", "$$hashKey": "object:146", "aggregation": "Last", "units": "none", "decimals": 2, "displayAliasType": "Warning / Critical", "displayValueWithAlias": "Never"}], "thresholds": [], "timeRegions": [], "title": "Crm stream events processing", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1059", "format": "s", "label": "Time to process all events", "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1060", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}, "timeFrom": null, "timeShift": null, "bars": false, "dashes": false, "fillGradient": 0, "hiddenSeries": false, "percentage": false, "points": false, "stack": false, "steppedLine": false, "datasource": null}, {"aliasColors": {}, "dashLength": 10, "fieldConfig": {"defaults": {"custom": {}, "color": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "s"}, "overrides": []}, "fill": 1, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 34}, "id": 35, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "pointradius": 2, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "targets": [{"expr": "rate(transaction_stream_worker_processing_all_threads_sum{pod=\"$podname\"}[10m])/rate(transaction_stream_worker_processing_all_threads_count{pod=\"$podname\"}[10m])", "legendFormat": "{{service}}  {{pod}}", "interval": "", "refId": "A", "valueHandler": "Number Threshold", "displayType": "Regular", "$$hashKey": "object:146", "aggregation": "Last", "units": "none", "decimals": 2, "displayAliasType": "Warning / Critical", "displayValueWithAlias": "Never"}], "thresholds": [], "timeRegions": [], "title": "Transaction stream events", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1059", "format": "s", "label": "Time to process all events", "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1060", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}, "timeFrom": null, "timeShift": null, "bars": false, "dashes": false, "fillGradient": 0, "hiddenSeries": false, "percentage": false, "points": false, "stack": false, "steppedLine": false, "datasource": null}, {"aliasColors": {}, "dashLength": 10, "fieldConfig": {"defaults": {"custom": {}, "color": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "s"}, "overrides": []}, "fill": 1, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 25}, "id": 37, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "pointradius": 2, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "targets": [{"expr": "rate(bonuses_stream_worker_processing_all_threads_sum{pod=\"$podname\"}[10m])/rate(bonuses_stream_worker_processing_all_threads_count{pod=\"$podname\"}[10m])", "legendFormat": "{{service}}  {{pod}}", "interval": "", "refId": "A", "valueHandler": "Number Threshold", "displayType": "Regular", "$$hashKey": "object:146", "aggregation": "Last", "units": "none", "decimals": 2, "displayAliasType": "Warning / Critical", "displayValueWithAlias": "Never"}], "thresholds": [], "timeRegions": [], "title": "Bonuses stream events processing", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1059", "format": "s", "label": "Time to process all events", "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1060", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}, "timeFrom": null, "timeShift": null, "bars": false, "dashes": false, "fillGradient": 0, "hiddenSeries": false, "percentage": false, "points": false, "stack": false, "steppedLine": false, "datasource": null}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "hiddenSeries": false, "id": 34, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(player_missions_processing_sum{pod=\"$podname\"}[10m])/rate(player_missions_processing_count{pod=\"$podname\"}[10m])", "interval": "", "legendFormat": "{{service}}  {{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Player missions processing", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "hiddenSeries": false, "id": 24, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(player_event_to_coin_processing_sum{pod=\"$podname\"}[10m])/rate(player_event_to_coin_processing_count{pod=\"$podname\"}[10m])", "interval": "", "legendFormat": "{{service}}  {{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Player event to coin processing", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:585", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:586", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(player_mission_processing_sum{pod=\"$podname\"}[10m])/rate(player_mission_processing_count{pod=\"$podname\"}[10m])", "hide": false, "interval": "", "legendFormat": "{{service}}  {{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Player mission processing", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:91", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:92", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "hiddenSeries": false, "id": 28, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(create_player_journey_processing_sum{pod=\"$podname\"}[10m])/rate(create_player_journey_processing_count{pod=\"$podname\"}[10m])", "interval": "", "legendFormat": "{{service}}  {{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Create player journey processing", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:977", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:978", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 17}, "hiddenSeries": false, "id": 26, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(stream_event_processing_sum{pod=\"$podname\"}[10m])/rate(stream_event_processing_count{pod=\"$podname\"}[10m])", "interval": "", "legendFormat": "{{service}}  {{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Stream event processing", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:895", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:896", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 17}, "hiddenSeries": false, "id": 32, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(player_mission_processing_sum{pod=\"$podname\"}[10m])/rate(player_mission_processing_count{pod=\"$podname\"}[10m]) + rate(player_event_to_coin_processing_sum{pod=\"$podname\"}[10m])/rate(player_event_to_coin_processing_count{pod=\"$podname\"}[10m]) + rate(stream_event_processing_sum{pod=\"$podname\"}[10m])/rate(stream_event_processing_count{pod=\"$podname\"}[10m]) + rate(create_player_journey_processing_sum{pod=\"$podname\"}[10m])/rate(create_player_journey_processing_count{pod=\"$podname\"}[10m]) + rate(player_journey_processing_sum{pod=\"$podname\"}[10m])/rate(player_journey_processing_count{pod=\"$podname\"}[10m])", "interval": "", "legendFormat": "{{service}}  {{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Total stream event processing", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:977", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:978", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 25}, "hiddenSeries": false, "id": 30, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(player_journey_processing_sum{pod=\"$podname\"}[10m])/rate(player_journey_processing_count{pod=\"$podname\"}[10m])", "interval": "", "legendFormat": "{{service}}  {{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Player journey processing", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1059", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1060", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "ews-game-journey-worker", "value": "ews-game-journey-worker"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{}, job)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Job", "multi": false, "name": "job", "options": [], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/ews-game-journey-worker.*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": "ews-dev", "value": "ews-dev"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "*************:80", "value": "*************:80"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Instance", "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-game-journey-worker-d5c87d78d-fm2vx", "value": "ews-game-journey-worker-d5c87d78d-fm2vx"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "description": null, "error": null, "hide": 2, "includeAll": false, "label": "Pod", "multi": false, "name": "pod", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-game-journey-worker-d5c87d78d-fm2vx", "value": "ews-game-journey-worker-d5c87d78d-fm2vx"}, "datasource": null, "definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Podname", "multi": false, "name": "podname", "options": [], "query": {"query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Game journey custom metrics", "uid": "U9f6sghtyprod", "version": 5}