{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "iteration": 1751013883712, "links": [], "panels": [{"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 23, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"editorMode": "code", "expr": "sum by (BusinessUnit, pod) (\r\n  increase(given_bonus_award_counter{namespace=\"$namespace\", pod=~\".*\"}[1h])\r\n  * on (namespace, pod)\r\n  group_left(workload, workload_type)\r\n  namespace_workload_pod:kube_pod_owner:relabel{\r\n    namespace=\"$namespace\",\r\n    workload=~\"$stream_events_processor_name\",\r\n    workload_type=~\".*\"\r\n  }\r\n)\r\n", "legendFormat": "{{pod}} - {{BusinessUnit}}", "range": true, "refId": "A"}], "title": "Given Bonus Awards Count", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 25, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"editorMode": "code", "expr": "sum by (BusinessUnit, pod) (\r\n  increase(given_player_coins_counter{namespace=\"$namespace\", pod=~\".*\"}[1h])\r\n  * on (namespace, pod)\r\n  group_left(workload, workload_type)\r\n  namespace_workload_pod:kube_pod_owner:relabel{\r\n    namespace=\"$namespace\",\r\n    workload=~\"$stream_events_processor_name\",\r\n    workload_type=~\".*\"\r\n  }\r\n)", "legendFormat": "{{pod}} - {{BusinessUnit}}", "range": true, "refId": "A"}], "title": "Given Coins Awards Count", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 5, "y": 8}, "id": 24, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"editorMode": "code", "expr": "sum by (BusinessUnit, pod) (\r\n  increase(created_player_mission_Counter{namespace=\"$namespace\", pod=~\".*\"}[1h])\r\n  * on (namespace, pod)\r\n  group_left(workload, workload_type)\r\n  namespace_workload_pod:kube_pod_owner:relabel{\r\n    namespace=\"$namespace\",\r\n    workload=~\"$stream_events_processor_name\",\r\n    workload_type=~\".*\"\r\n  }\r\n)", "legendFormat": "{{pod}} - {{BusinessUnit}}", "range": true, "refId": "A"}], "title": "Created Player Missions Count", "type": "timeseries"}], "refresh": "", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": true, "text": "ews-sof-prod", "value": "ews-sof-prod"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-gamification-stream-events-processor", "value": "ews-gamification-stream-events-processor"}, "datasource": null, "definition": "label_values(namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\"},workload)", "description": null, "error": null, "hide": 2, "includeAll": false, "label": "Stream Events Processor Name", "multi": false, "name": "stream_events_processor_name", "options": [], "query": {"query": "label_values(namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\"},workload)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/ews-gamification-stream-events-processor.*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"isNone": true, "selected": true, "text": "None", "value": ""}, "datasource": null, "definition": "label_values(process_private_memory_bytes{}, job)", "description": "job", "error": null, "hide": 2, "includeAll": false, "label": "job", "multi": false, "name": "job", "options": [{"isNone": true, "selected": true, "text": "None", "value": ""}], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 0, "regex": "/ews-gamification.*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Gamification Player Activities", "uid": "6s2svhbvibh56s43dasgty", "version": 2}