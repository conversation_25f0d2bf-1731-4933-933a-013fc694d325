{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "iteration": 1751612561340, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 91, "panels": [], "title": "Counters", "type": "row"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 1}, "id": 78, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(messages_consumed{job=~\"$job\", namespace=~\"$namespace\", topic=~\"feed-updates-.*\"}[1m]) * 60) by (update_type, provider, is_live, is_virtual, topic)", "instant": false, "interval": "", "legendFormat": " {{update_type}} - {{provider}} - IsLive({{is_live}}) | {{topic}}", "refId": "A"}], "title": "Messages In Feed Update Topics", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 1}, "id": 86, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(messages_consumed{job=~\"$job\", namespace=~\"$namespace\", topic=~\"feed-aggregator\"}[1m]) * 60) by (update_type, provider, is_live, is_virtual)", "instant": false, "interval": "", "legendFormat": " {{update_type}} - {{provider}} - IsLive({{is_live}})", "refId": "A"}], "title": "Messages In Feed Aggregator Topic", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 1}, "id": 87, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(messages_consumed{job=~\"$job\", namespace=~\"$namespace\", topic=~\"unified-feed\"}[1m]) * 60) by (update_type, provider, is_live, is_virtual)", "instant": false, "interval": "", "legendFormat": " {{update_type}} - {{provider}} - IsLive({{is_live}})", "refId": "A"}], "title": "Messages In Unified Feed Topic", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 1}, "id": 70, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(flow_errors{job=~\"$job\", namespace=~\"$namespace\"}[1m]) * 60) by (error,topic)", "interval": "", "legendFormat": "{{topic}} - {{error}} ", "refId": "A"}], "title": "Flow Errors", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 9, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 99, "options": {"legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"editorMode": "code", "expr": "sum(rate(messages_consumed{job=~\"$job\", namespace=~\"$namespace\", topic=~\"sport.feed-aggregator.feed-aggregator-adapted\"}[1m]) * 60) by (update_type, provider, is_live, is_virtual)", "legendFormat": " {{update_type}} - {{provider}} - IsLive({{is_live}})", "range": true, "refId": "A"}], "title": "Messages in Feed Aggregator Adapted Topic", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "id": 100, "options": {"legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"editorMode": "code", "expr": "sum(rate(messages_consumed{job=~\"$job\", namespace=~\"$namespace\", topic=~\"sport.feed-aggregator.market-unifier\"}[1m]) * 60) by (update_type, provider, is_live, is_virtual)", "legendFormat": " {{update_type}} - {{provider}} - IsLive({{is_live}})", "range": true, "refId": "A"}], "title": "Messages in Market Unifier Topic", "type": "timeseries"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "id": 89, "panels": [], "title": "Latency", "type": "row"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 18}, "id": 69, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "avg(rate(delay_in_provider_sum{job=~\"$job\", namespace=~\"$namespace\", topic=~\"feed-updates-.*\"}[5m])/rate(delay_in_provider_count{job=~\"$job\", namespace=~\"$namespace\", topic=~\"feed-updates-.*\"}[5m])) by (provider, is_live, is_virtual)", "interval": "", "legendFormat": "{{provider}} - IsLive({{is_live}})", "refId": "A"}], "title": "Delay In Provider (Before first EGT consumer took the update)", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 25}, "id": 80, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "avg(rate(previous_application_processing_time_sum{job=~\"$job\", namespace=~\"$namespace\", topic=~\"feed-updates-.*\"}[5m])/rate(previous_application_processing_time_count{job=~\"$job\", namespace=~\"$namespace\", topic=~\"feed-updates-.*\"}[5m])) by (provider, is_live, is_virtual)", "interval": "", "legendFormat": "{{provider}} - IsLive({{is_live}})", "refId": "A"}], "title": "Feed Adapters Processing Time", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 25}, "id": 84, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "avg(rate(previous_application_processing_time_sum{job=~\"$job\", namespace=~\"$namespace\", topic=~\"feed-aggregator\"}[5m])/rate(previous_application_processing_time_count{job=~\"$job\", namespace=~\"$namespace\", topic=~\"feed-aggregator\"}[5m])) by (provider, is_live, is_virtual)", "interval": "", "legendFormat": "{{provider}} - IsLive({{is_live}})", "refId": "A"}], "title": "Feed Hub Processing Time", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 25}, "id": 82, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "avg(rate(previous_application_processing_time_sum{job=~\"$job\", namespace=~\"$namespace\", topic=~\"unified-feed\"}[5m])/rate(previous_application_processing_time_count{job=~\"$job\", namespace=~\"$namespace\", topic=~\"unified-feed\"}[5m])) by (provider, is_live, is_virtual)", "interval": "", "legendFormat": "{{provider}} - IsLive({{is_live}})", "refId": "A"}], "title": "Feed Aggregator Processing Time", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 25}, "id": 96, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "avg(rate(previous_application_processing_time_sum{job=~\"$job\", namespace=~\"$namespace\", topic=~\"sport-offering-feed\"}[5m])/rate(previous_application_processing_time_count{job=~\"$job\", namespace=~\"$namespace\", topic=~\"sport-offering-feed\"}[5m])) by (provider, is_live, is_virtual)", "interval": "", "legendFormat": "{{provider}} - IsLive({{is_live}})", "refId": "A"}], "title": "Trading Processing Time", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 33}, "id": 83, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "avg(rate(total_latency_sum{job=~\"$job\", namespace=~\"$namespace\", topic=~\"feed-updates-.*\"}[5m])/rate(total_latency_count{job=~\"$job\", namespace=~\"$namespace\", topic=~\"feed-updates-.*\"}[5m])) by (provider, is_live, is_virtual)", "interval": "", "legendFormat": "{{provider}} - IsLive({{is_live}})", "refId": "A"}], "title": "Feed Adapters Total Latency", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 33}, "id": 81, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "avg(rate(total_latency_sum{job=~\"$job\", namespace=~\"$namespace\", topic=~\"feed-aggregator\"}[5m])/rate(total_latency_count{job=~\"$job\", namespace=~\"$namespace\", topic=~\"feed-aggregator\"}[5m])) by (provider, is_live, is_virtual)", "interval": "", "legendFormat": "{{provider}} - IsLive({{is_live}})", "refId": "A"}], "title": "Feed Hub Total Latency", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 33}, "id": 85, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "avg(rate(total_latency_sum{job=~\"$job\", namespace=~\"$namespace\", topic=~\"unified-feed\"}[5m])/rate(total_latency_count{job=~\"$job\", namespace=~\"$namespace\", topic=~\"unified-feed\"}[5m])) by (provider, is_live, is_virtual) > 0", "interval": "", "legendFormat": "{{provider}} - IsLive({{is_live}})", "refId": "A"}], "title": "Feed Aggregator Total Latency", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 33}, "id": 95, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "avg(rate(total_latency_sum{job=~\"$job\", namespace=~\"$namespace\", topic=~\"sport-offering-feed\"}[5m])/rate(total_latency_count{job=~\"$job\", namespace=~\"$namespace\", topic=~\"sport-offering-feed\"}[5m])) by (provider, is_live, is_virtual)", "interval": "", "legendFormat": "{{provider}} - IsLive({{is_live}})", "refId": "A"}], "title": "Trading Total Latency", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 41}, "id": 102, "options": {"legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"editorMode": "code", "expr": "avg(rate(previous_application_processing_time_sum{job=~\"$job\", namespace=~\"$namespace\", topic=~\"sport.feed-aggregator.market-unifier\"}[5m])/rate(previous_application_processing_time_count{job=~\"$job\", namespace=~\"$namespace\", topic=~\"sport.feed-aggregator.market-unifier\"}[5m])) by (provider, is_live, is_virtual)", "legendFormat": "{{provider}} - IsLive({{is_live}})", "range": true, "refId": "A"}], "title": "Market Unifier Processing Time", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 41}, "id": 101, "options": {"legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"editorMode": "code", "expr": "avg(rate(total_latency_sum{job=~\"$job\", namespace=~\"$namespace\", topic=~\"sport.feed-aggregator.market-unifier\"}[5m])/rate(total_latency_count{job=~\"$job\", namespace=~\"$namespace\", topic=~\"sport.feed-aggregator.market-unifier\"}[5m])) by (provider, is_live, is_virtual) > 0", "legendFormat": "{{provider}} - IsLive({{is_live}})", "range": true, "refId": "A"}], "title": "Market Unifier Total Latency", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 49}, "id": 77, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "avg(rate(previous_application_processing_time_sum{job=~\"$job\", namespace=~\"$namespace\"}[5m])/rate(previous_application_processing_time_count{job=~\"$job\", namespace=~\"$namespace\"}[5m])) by (topic, provider, is_live, is_virtual)", "interval": "", "legendFormat": "{{topic}} - {{provider}} - IsLive({{is_live}})", "refId": "A"}], "title": "Producer Processing Time", "type": "timeseries"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 56}, "id": 42, "panels": [], "title": "Kafka Lag", "type": "row"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 6, "x": 0, "y": 57}, "id": 48, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(kafka_consumergroup_lag{topic=~\"feed-updates-.*\",consumergroup!~\".*Local.*\"}) by (consumergroup)", "interval": "", "legendFormat": "{{consumergroup}}", "refId": "A"}], "title": "feed-updates-* consumers", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 6, "x": 6, "y": 57}, "id": 92, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(kafka_consumergroup_lag{topic=~\"feed-aggregator\",consumergroup!~\".*Local.*\"}) by (consumergroup)", "interval": "", "legendFormat": "{{consumergroup}}", "refId": "A"}], "title": "feed-aggregator consumers", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 6, "x": 12, "y": 57}, "id": 93, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(kafka_consumergroup_lag{topic=~\"unified-feed\",consumergroup!~\".*Local.*\"}) by (consumergroup, namespace)", "interval": "", "legendFormat": "{{consumergroup}} [{{namespace}}]", "refId": "A"}], "title": "unified-feed consumers", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 6, "x": 18, "y": 57}, "id": 94, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(kafka_consumergroup_lag{topic=~\"sport-offering-feed|sport-offering-virtual-feed|sport-offering-config-feed\",consumergroup!~\".*Local.*\"}) by (consumergroup, namespace, topic)", "interval": "", "legendFormat": "{{consumergroup}} [{{namespace}}] | {{topic}}", "refId": "A"}], "title": "unified-feed consumers", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 66}, "id": 97, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"editorMode": "code", "expr": "sum(kafka_consumergroup_lag{topic=~\"sport.feed-aggregator.market-unifier\",consumergroup!~\".*Local.*\"}) by (consumergroup)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "sport.feed-aggregator.market-unifier consumers", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 66}, "id": 98, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"editorMode": "code", "expr": "sum(kafka_consumergroup_lag{topic=~\"sport.feed-aggregator.feed-aggregator-adapted\",consumergroup!~\".*Local.*\"}) by (consumergroup)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "sport.feed-aggregator.feed-aggregator-adapted consumers", "type": "timeseries"}], "refresh": "5s", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": "all", "current": {"text": "ews-feeds-metric-collector", "value": "ews-feeds-metric-collector"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(process_private_memory_bytes{}, job)", "description": null, "error": {}, "hide": 0, "includeAll": false, "label": "Job", "multi": false, "name": "job", "options": [], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/ews-feeds-metric-collector/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-sof-prod", "value": "ews-sof-prod"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"text": ["All"], "value": ["$__all"]}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "description": null, "error": {}, "hide": 2, "includeAll": true, "label": "Instance", "multi": true, "name": "instance", "options": [], "query": "", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"text": "ews-feeds-metric-collector-d88f5bf66-l58b2", "value": "ews-feeds-metric-collector-d88f5bf66-l58b2"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "description": null, "error": {}, "hide": 2, "includeAll": false, "label": "Pod", "multi": false, "name": "pod", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"text": "ews-feeds-metric-collector-d88f5bf66-l58b2", "value": "ews-feeds-metric-collector-d88f5bf66-l58b2"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "description": null, "error": {}, "hide": 2, "includeAll": false, "label": "Podname", "multi": false, "name": "podname", "options": [], "query": {"query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Feed Metrics", "uid": "WLF_VMvddvGHz", "version": 2}