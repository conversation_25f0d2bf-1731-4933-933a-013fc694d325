#!/usr/bin/env pwsh

# Script to find dashboard.json files with hardcoded namespace values in queries
# Looks for namespace="some-value" where some-value is not $namespace

Write-Host "Searching for dashboard.json files with hardcoded namespace values..." -ForegroundColor Yellow
Write-Host ""

$foundFiles = @()
$totalFiles = 0

# Get all dashboard.json files recursively
$dashboardFiles = Get-ChildItem -Path . -Name "dashboard.json" -Recurse

foreach ($file in $dashboardFiles) {
    $totalFiles++
    $fullPath = $file
    
    try {
        $content = Get-Content $fullPath -Raw -ErrorAction Stop
        
        # Look for hardcoded namespace patterns
        # Pattern 1: namespace="ews-something" (common EWS namespace pattern)
        # Pattern 2: namespace="any-literal-value" but NOT namespace="$namespace"
        $hardcodedMatches = @()
        
        # Find all namespace="..." patterns
        $namespaceMatches = [regex]::Matches($content, 'namespace="([^"]+)"')
        
        foreach ($match in $namespaceMatches) {
            $namespaceValue = $match.Groups[1].Value
            
            # Skip if it's a variable (starts with $)
            if (-not $namespaceValue.StartsWith('$')) {
                $hardcodedMatches += $match.Value
            }
        }
        
        if ($hardcodedMatches.Count -gt 0) {
            $foundFiles += [PSCustomObject]@{
                File = $fullPath
                HardcodedNamespaces = $hardcodedMatches
            }
            
            Write-Host "Found hardcoded namespace(s) in: $fullPath" -ForegroundColor Red
            foreach ($match in $hardcodedMatches) {
                Write-Host "  - $match" -ForegroundColor White
            }
            Write-Host ""
        }
    }
    catch {
        Write-Warning "Error reading file $fullPath`: $_"
    }
}

Write-Host "Summary:" -ForegroundColor Cyan
Write-Host "Total dashboard.json files processed: $totalFiles"
Write-Host "Files with hardcoded namespaces: $($foundFiles.Count)"

if ($foundFiles.Count -gt 0) {
    Write-Host ""
    Write-Host "Files that need to be updated:" -ForegroundColor Yellow
    foreach ($file in $foundFiles) {
        Write-Host "- $($file.File)" -ForegroundColor White
    }
}
