# PowerShell script to remove *-prod.json files that have been converted to proper dashboard structures
param(
    [switch]$DryRun = $false
)

function Test-DashboardStructureExists {
    param(
        [string]$JsonFilePath,
        [string]$ParentFolder
    )
    
    $jsonFileName = Split-Path $JsonFilePath -Leaf
    # Remove -prod.json or -prod copy.json suffix to get the dashboard name
    $dashboardName = $jsonFileName -replace '-prod( copy)?\.json$', ''
    
    # Check if the dashboard structure exists
    $targetDir = Join-Path $ParentFolder $dashboardName
    
    if (Test-Path $targetDir) {
        $dashboardJson = Join-Path $targetDir "dashboard.json"
        $dashboardYaml = Join-Path $targetDir "dashboard.yaml"
        $kustomizationYaml = Join-Path $targetDir "kustomization.yaml"
        
        return (Test-Path $dashboardJson) -and (Test-Path $dashboardYaml) -and (Test-Path $kustomizationYaml)
    }
    
    return $false
}

function Remove-ProcessedProdJsonFile {
    param(
        [string]$JsonFilePath,
        [string]$ParentFolder
    )
    
    $jsonFileName = Split-Path $JsonFilePath -Leaf
    $dashboardName = $jsonFileName -replace '-prod( copy)?\.json$', ''
    
    Write-Host "Checking: $JsonFilePath" -ForegroundColor Yellow
    
    if (Test-DashboardStructureExists $JsonFilePath $ParentFolder) {
        Write-Host "  Dashboard structure exists for: $dashboardName" -ForegroundColor Green
        
        if (-not $DryRun) {
            try {
                Remove-Item $JsonFilePath -Force
                Write-Host "  Removed: $JsonFilePath" -ForegroundColor Green
                return $true
            }
            catch {
                Write-Host "  Error removing $JsonFilePath`: $_" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "  Would remove: $JsonFilePath" -ForegroundColor Gray
            return $true
        }
    } else {
        Write-Host "  Dashboard structure NOT found for: $dashboardName - SKIPPING" -ForegroundColor Red
        return $false
    }
}

# Main execution
Write-Host "Removing processed *-prod.json files..." -ForegroundColor Magenta
Write-Host "Dry run mode: $DryRun" -ForegroundColor Magenta
Write-Host ""

# Get all directories in current path
$directories = Get-ChildItem -Path . -Directory

$totalFilesFound = 0
$filesRemoved = 0
$filesSkipped = 0

foreach ($dir in $directories) {
    $dirName = $dir.Name
    
    # Skip certain directories that we know don't have processed files
    if ($dirName -match "^(k8s-dashboard|noc-dashboards-prod|resource-usage-prod|telegraf-metrics-prod|vault-prod|clickhouse-prod|coherence|elasticsearch|ews-bi|ews-cache-populator|ews-core|ews-database|ews-datascience|ews-ps|fluentbit|logstash)$") {
        continue
    }
    
    # Find all *-prod.json files in the directory root (not subdirectories), including copy files
    $prodJsonFiles = Get-ChildItem -Path $dir.FullName -Name "*-prod*.json" | Where-Object { $_ -notmatch "\\" -and ($_ -match "-prod\.json$" -or $_ -match "-prod copy\.json$") }
    
    if ($prodJsonFiles.Count -gt 0) {
        Write-Host "Found $($prodJsonFiles.Count) *-prod.json files in $dirName" -ForegroundColor White
        $totalFilesFound += $prodJsonFiles.Count
        
        foreach ($jsonFile in $prodJsonFiles) {
            $fullPath = Join-Path $dir.FullName $jsonFile
            if (Remove-ProcessedProdJsonFile $fullPath $dirName) {
                $filesRemoved++
            } else {
                $filesSkipped++
            }
        }
        Write-Host ""
    }
}

# Handle root-level files
$rootProdFiles = Get-ChildItem -Path . -Name "*-prod*.json" | Where-Object { $_ -match "-prod\.json$" -or $_ -match "-prod copy\.json$" }
if ($rootProdFiles.Count -gt 0) {
    Write-Host "Found $($rootProdFiles.Count) *-prod.json files in root directory" -ForegroundColor White
    $totalFilesFound += $rootProdFiles.Count
    
    foreach ($jsonFile in $rootProdFiles) {
        # For root files, the dashboard structure should be in a folder with the dashboard name
        $dashboardName = $jsonFile -replace '-prod( copy)?\.json$', ''
        
        Write-Host "Checking root file: $jsonFile" -ForegroundColor Yellow
        
        if (Test-Path $dashboardName) {
            $dashboardJson = Join-Path $dashboardName "dashboard.json"
            $dashboardYaml = Join-Path $dashboardName "dashboard.yaml"
            $kustomizationYaml = Join-Path $dashboardName "kustomization.yaml"
            
            if ((Test-Path $dashboardJson) -and (Test-Path $dashboardYaml) -and (Test-Path $kustomizationYaml)) {
                Write-Host "  Dashboard structure exists for: $dashboardName" -ForegroundColor Green
                
                if (-not $DryRun) {
                    try {
                        Remove-Item $jsonFile -Force
                        Write-Host "  Removed: $jsonFile" -ForegroundColor Green
                        $filesRemoved++
                    }
                    catch {
                        Write-Host "  Error removing $jsonFile`: $_" -ForegroundColor Red
                        $filesSkipped++
                    }
                } else {
                    Write-Host "  Would remove: $jsonFile" -ForegroundColor Gray
                    $filesRemoved++
                }
            } else {
                Write-Host "  Dashboard structure NOT complete for: $dashboardName - SKIPPING" -ForegroundColor Red
                $filesSkipped++
            }
        } else {
            Write-Host "  Dashboard structure NOT found for: $dashboardName - SKIPPING" -ForegroundColor Red
            $filesSkipped++
        }
    }
}

Write-Host ""
Write-Host "Summary:" -ForegroundColor Magenta
Write-Host "  Total *-prod.json files found: $totalFilesFound" -ForegroundColor White
Write-Host "  Files removed: $filesRemoved" -ForegroundColor Green
Write-Host "  Files skipped: $filesSkipped" -ForegroundColor Yellow

if ($DryRun) {
    Write-Host "  This was a dry run - no files were actually removed" -ForegroundColor Yellow
} else {
    Write-Host "  Cleanup completed!" -ForegroundColor Green
}
