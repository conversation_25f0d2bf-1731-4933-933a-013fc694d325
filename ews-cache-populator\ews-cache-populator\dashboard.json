{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 5005, "links": [], "panels": [{"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 14, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(execution_failed_total{job=\"$job\",namespace=\"$namespace\"  }[1m])", "interval": "", "legendFormat": "{{action}} [{{code}}] {{controller}}", "refId": "A"}], "title": "Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 36, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(processing_queue_lag{job=\"$job\",namespace=\"$namespace\"}[1m])", "interval": "", "legendFormat": "{{queue}}", "refId": "A"}], "title": "Processing Lags", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 22, "panels": [], "title": "General", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 9}, "id": 34, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(execution_duration_seconds_sum{job=\"$job\",namespace=\"$namespace\",action=\"GetNextDiffAsync\"}[5m])/rate(execution_duration_seconds_count{job=\"$job\",namespace=\"$namespace\",action=\"GetNextDiffAsync\"}[5m])", "interval": "", "legendFormat": "{{controller}}", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "rate(execution_duration_seconds_sum{job=\"$job\",namespace=\"$namespace\",action=\"ProduceAsync\"}[5m])/rate(execution_duration_seconds_count{job=\"$job\",namespace=\"$namespace\",action=\"ProduceAsync\"}[5m])", "hide": false, "interval": "", "legendFormat": "{{action}}", "refId": "B"}, {"datasource": {"type": "prometheus"}, "expr": "rate(execution_duration_seconds_sum{job=\"$job\",namespace=\"$namespace\",action=\"Flush\"}[5m])/rate(execution_duration_seconds_count{job=\"$job\",namespace=\"$namespace\",action=\"Flush\"}[5m])", "hide": false, "interval": "", "legendFormat": "{{action}}", "refId": "C"}], "title": "(DIFF) Execution Durations in Seconds", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 9}, "id": 39, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(execution_duration_seconds_sum{job=\"$job\",namespace=\"$namespace\",action=\"FullCacheLoadAsync\"}[5m])/rate(execution_duration_seconds_count{job=\"$job\",namespace=\"$namespace\",action=\"FullCacheLoadAsync\"}[5m])", "interval": "", "legendFormat": "{{controller}}", "refId": "A"}], "title": "(FULL) Execution Durations in Seconds", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 18}, "id": 35, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(execution_processed_count_total{job=\"$job\",namespace=\"$namespace\",action=\"ProduceAsync\"}[1m])", "interval": "", "legendFormat": "{{controller}}", "refId": "A"}], "title": " (DIFF) Processed Items per sec", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 18}, "id": 41, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(execution_processed_count_total{job=\"$job\",namespace=\"$namespace\",action=\"FullCacheLoadAsync\"}[1m])", "interval": "", "legendFormat": "{{controller}}", "refId": "A"}], "title": " (FULL) Processed Items per sec", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 27}, "id": 40, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(count_object_state_dedpulcations{job=\"$job\",namespace=\"$namespace\"}[1m])) by (type)", "hide": false, "interval": "", "legendFormat": "{{type}}MemstateCache", "refId": "B"}], "title": "MemstateCache Deduplications Rate", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 34}, "id": 42, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(count_full_load_update_types{job=\"$job\",namespace=\"$namespace\"}[$__rate_interval])) by (type, operation) * 60", "hide": false, "interval": "", "legendFormat": " {{type}}sCache Full Load {{operation}}", "refId": "B"}], "title": "Full Load Cache Updates Count", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 41}, "id": 43, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(count_full_load_update_types{job=\"$job\",namespace=\"$namespace\"}[$__rate_interval])) by (type, operation)", "hide": false, "interval": "", "legendFormat": " {{type}}sCache Full Load {{operation}} /sec", "refId": "B"}], "title": "Full Load Cache Update Rates", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 48}, "id": 38, "panels": [{"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 28}, "id": 2, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(process_cpu_seconds_total{job=\"$job\",namespace=\"$namespace\"}[5m]))", "interval": "", "legendFormat": "cpu usage", "refId": "A"}], "title": "Number of cores used", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 28}, "id": 24, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_private_memory_bytes{job=\"$job\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_private_memory_bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 36}, "id": 26, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_open_handles{job=\"$job\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_open_handles", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 36}, "id": 32, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_working_set_bytes{job=\"$job\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_working_set_bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 44}, "id": 30, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_start_time_seconds{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_start_time_seconds", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 44}, "id": 28, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_num_threads{job=\"$job\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_num_threads", "type": "timeseries"}], "title": "Resources", "type": "row"}], "preload": false, "refresh": "5s", "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "", "value": ""}, "label": "Feed Type", "name": "feed_type", "options": [{"selected": true, "text": "Premium", "value": ""}, {"selected": false, "text": "Standard", "value": "standard-"}], "query": "Premium :   ,Standard : standard-", "type": "custom"}, {"current": {"text": "ews-cache-populator", "value": "ews-cache-populator"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{}, job)", "includeAll": false, "label": "Job", "name": "job", "options": [], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/^${feed_type}ews-cache-populator.*/", "type": "query"}, {"current": {"text": "ews-dev", "value": "ews-dev"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "includeAll": false, "label": "Namespace", "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "172.17.57.215:80", "value": "172.17.57.215:80"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "includeAll": false, "label": "Instance", "name": "instance", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"current": {"text": "ews-cache-populator-7878989d8d-whtzb", "value": "ews-cache-populator-7878989d8d-whtzb"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "hide": 2, "includeAll": false, "label": "Pod", "name": "pod", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "ews-cache-populator-7878989d8d-whtzb", "value": "ews-cache-populator-7878989d8d-whtzb"}, "datasource": {"type": "prometheus"}, "definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "includeAll": false, "label": "Podname", "name": "podname", "options": [], "query": {"query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-12h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Kafka Cache Populator", "uid": "NzUnFO84k", "version": 1, "weekStart": ""}