# PowerShell script to convert *-prod.json files to proper dashboard structures
#.\convert-prod-dashboards.ps1 -DryRun | Select-String -Pattern "copy|Found.*files"
param(
    [switch]$DryRun = $false
)

function Get-DashboardName {
    param([string]$JsonFileName)

    # Remove -prod.json or -prod copy.json suffix to get the dashboard name
    $name = $JsonFileName -replace '-prod( copy)?\.json$', ''
    return $name
}

function Get-FolderRef {
    param([string]$ParentFolder)
    
    # Map parent folder names to folderRef values
    switch ($ParentFolder) {
        "ews-crm" { return "ews-crm" }
        "ews-frontend" { return "ews-frontend" }
        "ews-gaming" { return "ews-gaming" }
        "ews-infra" { return "ews-infra" }
        "ews-payments" { return "ews-payments" }
        "ews-retail" { return "ews-retail" }
        "ews-sports" { return "ews-sports" }
        "infrastructure" { return "infrastructure" }
        default { return $ParentFolder }
    }
}

function Create-KustomizationYaml {
    param(
        [string]$DashboardName,
        [string]$TargetDir
    )
    
    $kustomizationContent = @"
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - dashboard.yaml

generatorOptions:
  disableNameSuffixHash: true
  labels:
    grafana_dashboard: "true"
    
configMapGenerator:
  - name: $DashboardName
    files:
      - dashboard.json
"@
    
    $kustomizationPath = Join-Path $TargetDir "kustomization.yaml"
    if (-not $DryRun) {
        $kustomizationContent | Out-File -FilePath $kustomizationPath -Encoding UTF8 -NoNewline
    }
    return $kustomizationPath
}

function Create-DashboardYaml {
    param(
        [string]$DashboardName,
        [string]$FolderRef,
        [string]$TargetDir
    )
    
    $dashboardContent = @"
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaDashboard
metadata:
  name: $DashboardName
spec:
  folderRef: $FolderRef
  allowCrossNamespaceImport: true
  resyncPeriod: 30s
  instanceSelector:
    matchLabels:
      dashboards: "grafana"
  configMapRef:
    name: $DashboardName
    key: dashboard.json
"@
    
    $dashboardPath = Join-Path $TargetDir "dashboard.yaml"
    if (-not $DryRun) {
        $dashboardContent | Out-File -FilePath $dashboardPath -Encoding UTF8 -NoNewline
    }
    return $dashboardPath
}

function Process-ProdJsonFile {
    param(
        [string]$JsonFilePath,
        [string]$ParentFolder
    )

    $jsonFileName = Split-Path $JsonFilePath -Leaf
    $dashboardName = Get-DashboardName $jsonFileName
    $folderRef = Get-FolderRef $ParentFolder

    # Create target directory
    $targetDir = Join-Path $ParentFolder $dashboardName

    # Check if this is a copy file and if the target directory already exists or is in the processing queue
    $isCopyFile = $jsonFileName -match " copy\.json$"
    $targetExists = Test-Path $targetDir

    Write-Host "Processing: $JsonFilePath" -ForegroundColor Yellow
    Write-Host "  Dashboard name: $dashboardName" -ForegroundColor Cyan
    Write-Host "  Target directory: $targetDir" -ForegroundColor Cyan
    Write-Host "  Folder reference: $folderRef" -ForegroundColor Cyan

    # Skip copy files if the original has already been processed or will be processed
    if ($isCopyFile -and ($targetExists -or (Get-ChildItem -Path $ParentFolder -Name "$dashboardName-prod.json" | Where-Object { $_ -notmatch "\\" }))) {
        Write-Host "  Skipping copy file as the original file exists or will be processed" -ForegroundColor Yellow
        return
    }
    
    if (-not $DryRun) {
        # Create directory if it doesn't exist
        if (-not (Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        }
        
        # Copy JSON file to dashboard.json
        $dashboardJsonPath = Join-Path $targetDir "dashboard.json"
        Copy-Item $JsonFilePath $dashboardJsonPath -Force
        Write-Host "  Created: $dashboardJsonPath" -ForegroundColor Green
    } else {
        Write-Host "  Would create directory: $targetDir" -ForegroundColor Gray
        Write-Host "  Would copy JSON to: $(Join-Path $targetDir 'dashboard.json')" -ForegroundColor Gray
    }
    
    # Create kustomization.yaml
    $kustomizationPath = Create-KustomizationYaml $dashboardName $targetDir
    if (-not $DryRun) {
        Write-Host "  Created: $kustomizationPath" -ForegroundColor Green
    } else {
        Write-Host "  Would create: $kustomizationPath" -ForegroundColor Gray
    }
    
    # Create dashboard.yaml
    $dashboardYamlPath = Create-DashboardYaml $dashboardName $folderRef $targetDir
    if (-not $DryRun) {
        Write-Host "  Created: $dashboardYamlPath" -ForegroundColor Green
    } else {
        Write-Host "  Would create: $dashboardYamlPath" -ForegroundColor Gray
    }
    
    Write-Host ""
}

# Main execution
Write-Host "Converting *-prod.json files to proper dashboard structures..." -ForegroundColor Magenta
Write-Host "Dry run mode: $DryRun" -ForegroundColor Magenta
Write-Host ""

# Get all directories in current path
$directories = Get-ChildItem -Path . -Directory

$processedCount = 0
$totalFiles = 0

foreach ($dir in $directories) {
    $dirName = $dir.Name
    
    # Skip certain directories
    if ($dirName -match "^(k8s-dashboard|noc-dashboards-prod|resource-usage-prod|telegraf-metrics-prod|vault-prod|clickhouse-prod|coherence|elasticsearch|ews-bi|ews-cache-populator|ews-core|ews-database|ews-datascience|ews-ps|fluentbit|logstash)$") {
        continue
    }
    
    # Find all *-prod.json files in the directory root (not subdirectories), including copy files
    $prodJsonFiles = Get-ChildItem -Path $dir.FullName -Name "*-prod*.json" | Where-Object { $_ -notmatch "\\" -and ($_ -match "-prod\.json$" -or $_ -match "-prod copy\.json$") }
    
    if ($prodJsonFiles.Count -gt 0) {
        Write-Host "Found $($prodJsonFiles.Count) *-prod.json files in $dirName" -ForegroundColor White
        $totalFiles += $prodJsonFiles.Count
        
        foreach ($jsonFile in $prodJsonFiles) {
            $fullPath = Join-Path $dir.FullName $jsonFile
            Process-ProdJsonFile $fullPath $dirName
            $processedCount++
        }
    }
}

# Handle root-level files
$rootProdFiles = Get-ChildItem -Path . -Name "*-prod*.json" | Where-Object { $_ -match "-prod\.json$" -or $_ -match "-prod copy\.json$" }
if ($rootProdFiles.Count -gt 0) {
    Write-Host "Found $($rootProdFiles.Count) *-prod.json files in root directory" -ForegroundColor White
    $totalFiles += $rootProdFiles.Count
    
    foreach ($jsonFile in $rootProdFiles) {
        # For root files, we need to create a folder structure
        $dashboardName = Get-DashboardName $jsonFile
        $targetDir = $dashboardName
        
        Write-Host "Processing root file: $jsonFile" -ForegroundColor Yellow
        Write-Host "  Dashboard name: $dashboardName" -ForegroundColor Cyan
        Write-Host "  Target directory: $targetDir" -ForegroundColor Cyan
        
        if (-not $DryRun) {
            if (-not (Test-Path $targetDir)) {
                New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
            }
            
            $dashboardJsonPath = Join-Path $targetDir "dashboard.json"
            Copy-Item $jsonFile $dashboardJsonPath -Force
            Write-Host "  Created: $dashboardJsonPath" -ForegroundColor Green
        }
        
        $kustomizationPath = Create-KustomizationYaml $dashboardName $targetDir
        $dashboardYamlPath = Create-DashboardYaml $dashboardName "root" $targetDir
        
        if (-not $DryRun) {
            Write-Host "  Created: $kustomizationPath" -ForegroundColor Green
            Write-Host "  Created: $dashboardYamlPath" -ForegroundColor Green
        }
        
        $processedCount++
        Write-Host ""
    }
}

Write-Host "Summary:" -ForegroundColor Magenta
Write-Host "  Total *-prod.json files found: $totalFiles" -ForegroundColor White
Write-Host "  Files processed: $processedCount" -ForegroundColor White
if ($DryRun) {
    Write-Host "  This was a dry run - no files were actually created" -ForegroundColor Yellow
} else {
    Write-Host "  Dashboard structures created successfully!" -ForegroundColor Green
}
