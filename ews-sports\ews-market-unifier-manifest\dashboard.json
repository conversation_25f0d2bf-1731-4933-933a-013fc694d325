{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 4619, "links": [], "panels": [{"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 0, "y": 0}, "id": 5, "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(rate(flow_events{job=~\"$job\", namespace=~\"$namespace\"}[1m]) * 60) by (events, updateType)", "legendFormat": "{{events}} - {{updateType}}", "range": true, "refId": "A"}], "title": "Flow Events", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 7, "x": 9, "y": 0}, "id": 2, "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(critical_errors{job=~\"$job\", namespace=~\"$namespace\"}[1m]) * 60", "legendFormat": "{{error}}", "range": true, "refId": "A"}], "title": "Critical Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 0}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(pipeline_errors{job=~\"$job\", namespace=~\"$namespace\"}[1m]) * 60", "legendFormat": "{{error}}", "range": true, "refId": "A"}], "title": "Pipeline Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 0, "y": 8}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(flow_errors{job=~\"$job\", namespace=~\"$namespace\"}[1m]) * 60", "legendFormat": "{{error}}", "range": true, "refId": "A"}], "title": "Flow Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 7, "x": 9, "y": 8}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(missing_mapping{job=~\"$job\", namespace=~\"$namespace\"}[1m]) * 60", "legendFormat": "{{error}}", "range": true, "refId": "A"}], "title": "Missing Mapping Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 8}, "id": 6, "options": {"alertThreshold": true, "legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(block_size{job=~\"$job\", namespace=~\"$namespace\"}) by (block)", "format": "time_series", "interval": "", "legendFormat": "{{block}}", "range": true, "refId": "A"}], "title": "Block Size", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 16}, "id": 8, "options": {"alertThreshold": true, "legend": {"calcs": ["max", "sum", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "avg(rate(execution_duration_seconds_sum{job=~\"$job\", namespace=~\"$namespace\"}[5m])/rate(execution_duration_seconds_count{job=~\"$job\", namespace=~\"$namespace\"}[5m])) by (action, controller)", "interval": "", "legendFormat": " {{controller}}.{{action}}", "refId": "A"}], "title": "Execution Durations by Action", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 7, "x": 8, "y": 16}, "id": 9, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "avg(rate(marketunifier_message_processing_time_sum{job=~\"$job\", namespace=~\"$namespace\"}[5m])/rate(marketunifier_message_processing_time_count{job=~\"$job\", namespace=~\"$namespace\"}[5m])) by (feed)", "interval": "", "legendFormat": "{{feed}}", "range": true, "refId": "A"}], "title": "Execution Durations By Feed", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 15, "y": 16}, "id": 7, "options": {"alertThreshold": true, "legend": {"calcs": ["max", "sum", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "avg(rate(marketunifier_kafka_delay_seconds_sum{job=~\"$job\", namespace=~\"$namespace\"}[5m])/rate(marketunifier_kafka_delay_seconds_count{job=~\"$job\", namespace=~\"$namespace\"}[5m])) by (feed)", "interval": "", "legendFormat": "Time in Kafka in Seconds - {{feed}}", "range": true, "refId": "A"}], "title": "Service Time", "type": "timeseries"}], "preload": false, "schemaVersion": 40, "tags": ["sports", "feed", "market-unifier", "feed-aggregator-flow"], "templating": {"list": [{"current": {"text": "ews-market-unifier", "value": "ews-market-unifier"}, "definition": "label_values(process_private_memory_bytes,job)", "includeAll": false, "label": "Job", "name": "job", "options": [], "query": {"qryType": 1, "query": "label_values(process_private_memory_bytes,job)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "/ews-market-unifier.*/\n", "type": "query"}, {"current": {"text": "ews-dev", "value": "ews-dev"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\"},namespace)", "label": "Namespace", "name": "namespace", "options": [], "query": {"qryType": 1, "query": "label_values(process_private_memory_bytes{job=\"$job\"},namespace)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"current": {"text": "All", "value": "$__all"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "includeAll": true, "label": "Instance", "multi": true, "name": "instance", "options": [], "query": {"qryType": 1, "query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"current": {"text": "All", "value": "$__all"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "includeAll": true, "label": "Pod", "multi": true, "name": "pod", "options": [], "query": {"qryType": 1, "query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "All", "value": "$__all"}, "definition": "label_values(kafka_consumer_group_current_offset{namespace=\"$namespace\"},consumergroup)", "includeAll": true, "label": "Consumer Group", "multi": true, "name": "consumergroup", "options": [], "query": {"qryType": 1, "query": "label_values(kafka_consumer_group_current_offset{namespace=\"$namespace\"},consumergroup)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "MarketUnifierConsumer.*", "sort": 1, "type": "query"}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "Europe/Sofia", "title": "market-unifier-manifest", "uid": "eenqic1mz45j4a", "version": 19, "weekStart": ""}