{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "iteration": 1747044471494, "links": [], "panels": [{"datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 29, "panels": [], "repeat": "instance", "scopedVars": {"instance": {"selected": true, "text": "************:9780", "value": "************:9780"}}, "title": "Barman instance", "type": "row"}, {"cacheTimeout": null, "datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "displayName": "", "mappings": [], "thresholds": {"mode": "absolute", "steps": []}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 10, "w": 4, "x": 0, "y": 1}, "id": 14, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "scopedVars": {"instance": {"selected": true, "text": "************:9780", "value": "************:9780"}}, "targets": [{"expr": "(node_filesystem_size_bytes{instance=~\"$host\",mountpoint=\"/postgresql\"} - node_filesystem_free_bytes{instance=~\"$host\",mountpoint=\"/postgresql\"}) / node_filesystem_size_bytes{instance=~\"$host\",mountpoint=\"/postgresql\"} * 100", "hide": true, "interval": "", "legendFormat": "", "refId": "B"}, {"expr": "1 - (sum(node_filesystem_size_bytes{instance=~\"$host\"}) / sum(node_filesystem_free_bytes{instance=~\"$host\"}))", "hide": true, "interval": "", "legendFormat": "", "refId": "A"}, {"expr": "(node_filesystem_size_bytes{instance=~\"$host\",mountpoint=\"/postgresql\"} - node_filesystem_free_bytes{instance=~\"$host\",mountpoint=\"/postgresql\"}) / node_filesystem_size_bytes{instance=~\"$host\",mountpoint=\"/postgresql\"} * 100", "hide": false, "interval": "", "legendFormat": "", "refId": "C"}], "timeFrom": null, "timeShift": null, "title": "Storage usage", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"color": {}, "custom": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "decbytes"}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 4, "y": 1}, "hiddenSeries": false, "id": 16, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "scopedVars": {"instance": {"selected": true, "text": "************:9780", "value": "************:9780"}}, "seriesOverrides": [{"alias": "available disk space", "color": "#F2495C", "dashes": true, "spaceLength": 3}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_filesystem_size_bytes{instance=~\"$host\",mountpoint=\"/var/lib/barman\"} - node_filesystem_avail_bytes{instance=~\"$host\",mountpoint=\"/var/lib/barman\"}", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "$instance: used disk space", "refId": "A"}, {"expr": "node_filesystem_size_bytes{instance=~\"$host\",mountpoint=\"/postgresql\"}", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "$instance: available disk space", "refId": "B"}, {"expr": "node_filesystem_avail_bytes{instance=~\"$host\",mountpoint=\"/postgresql\"}", "hide": false, "interval": "", "legendFormat": "", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Backup partition (/var/lib/barman)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:81", "format": "decbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:82", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["#299c46", "#AD0317", "#AD0317"], "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 10, "w": 4, "x": 16, "y": 1}, "id": 2, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "pluginVersion": "6.2.5", "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "scopedVars": {"instance": {"selected": true, "text": "************:9780", "value": "************:9780"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "count(barman_up{instance=~\"$instance.*\"}) - sum(barman_up{instance=~\"$instance.*\"})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "1", "timeFrom": null, "timeShift": null, "title": "Barman errors", "type": "singlestat", "valueFontSize": "150%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 20, "y": 1}, "id": 30, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "scopedVars": {"instance": {"selected": true, "text": "************:9780", "value": "************:9780"}}, "targets": [{"expr": "count(barman_backups_total{instance=~\"$instance.*\"}) by (server)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{ server }} ", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Number of servers", "type": "stat"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["#299c46", "#AD0317", "#AD0317"], "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 5, "w": 4, "x": 20, "y": 6}, "id": 31, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "pluginVersion": "6.2.5", "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "scopedVars": {"instance": {"selected": true, "text": "************:9780", "value": "************:9780"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "sum(barman_backups_total{instance=~\"$instance.*\"})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "timeFrom": null, "timeShift": null, "title": "Total number of backups", "type": "singlestat", "valueFontSize": "150%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 11}, "id": 27, "panels": [], "repeat": "server", "scopedVars": {"server": {"selected": true, "text": "egt", "value": "egt"}}, "title": "Server $server", "type": "row"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "h", "gauge": {"maxValue": 120, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 10, "w": 4, "x": 0, "y": 12}, "id": 8, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "pluginVersion": "6.2.5", "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeat": null, "scopedVars": {"server": {"selected": true, "text": "egt", "value": "egt"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "hour(time()- barman_last_backup{instance=~\"$instance.*\", server=\"$server\"})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "$instance: {{ server }}", "refId": "A"}], "thresholds": "48", "timeFrom": null, "timeShift": null, "title": "Last backup age", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": null, "decimals": 1, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "s", "gauge": {"maxValue": 120, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 5, "w": 4, "x": 4, "y": 12}, "id": 24, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "pluginVersion": "6.2.5", "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "scopedVars": {"server": {"selected": true, "text": "egt", "value": "egt"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "time() - barman_first_backup{instance=~\"$instance.*\", server=\"$server\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "48", "timeFrom": null, "timeShift": null, "title": "First backup age", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": true, "colorPrefix": false, "colorValue": false, "colors": ["#299c46", "#F2495C", "#F2495C"], "datasource": null, "decimals": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 5, "w": 4, "x": 8, "y": 12}, "id": 23, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "pluginVersion": "6.2.5", "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "scopedVars": {"server": {"selected": true, "text": "egt", "value": "egt"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "barman_backups_failed{instance=~\"$instance.*\", server=\"$server\"}", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "1", "timeFrom": null, "timeShift": null, "title": "Failed backups", "type": "singlestat", "valueFontSize": "100%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 12}, "hiddenSeries": false, "id": 6, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "scopedVars": {"server": {"selected": true, "text": "egt", "value": "egt"}}, "seriesOverrides": [{"alias": ""}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "hour(time() - barman_last_backup{instance=~\"$instance.*\", server=\"$server\"})", "format": "time_series", "hide": false, "instant": false, "interval": "15m", "intervalFactor": 1, "legendFormat": "$instance: {{ server }}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Time since last backup", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:420", "decimals": 1, "format": "h", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:421", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": null, "decimals": 1, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "s", "gauge": {"maxValue": 120, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 5, "w": 4, "x": 4, "y": 17}, "id": 25, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "pluginVersion": "6.2.5", "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "scopedVars": {"server": {"selected": true, "text": "egt", "value": "egt"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "barman_last_backup_copy_time{instance=~\"$instance.*\", server=\"$server\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "48", "timeFrom": null, "timeShift": null, "title": "Last backup copy time", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["#AD0317", "#37872D", "#299c46"], "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 5, "w": 4, "x": 8, "y": 17}, "id": 19, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "pluginVersion": "6.2.5", "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "scopedVars": {"server": {"selected": true, "text": "egt", "value": "egt"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "barman_backups_total{instance=~\"$instance.*\", server=\"$server\"} - barman_backups_failed{instance=~\"$instance.*\", server=\"$server\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "4", "timeFrom": null, "timeShift": null, "title": "Available backups", "type": "singlestat", "valueFontSize": "100%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 22}, "hiddenSeries": false, "id": 20, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "scopedVars": {"server": {"selected": true, "text": "egt", "value": "egt"}}, "seriesOverrides": [{"alias": ""}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "barman_backup_size{instance=~\"$instance.*\", server=\"$server\", number=\"1\"}", "format": "time_series", "hide": false, "interval": "15m", "intervalFactor": 1, "legendFormat": "$instance: {{ server }} backup size", "refId": "A"}, {"expr": "barman_backup_wal_size{instance=~\"$instance.*\", server=\"$server\", number=\"1\"}", "format": "time_series", "interval": "15m", "intervalFactor": 1, "legendFormat": "$instance: {{ server }} WAL size", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Last backup size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "************:9780", "value": "************:9780"}, "datasource": "Prometheus", "definition": "label_values(barman_up, instance)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Instance", "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(barman_up, instance)", "refId": "Prometheus-instance-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"selected": true, "text": "egt", "value": "egt"}, "datasource": "Prometheus", "definition": "label_values(barman_up, server)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Server", "multi": false, "name": "server", "options": [{"selected": false, "text": "audit", "value": "audit"}, {"selected": true, "text": "egt", "value": "egt"}], "query": {"query": "label_values(barman_up, server)", "refId": "StandardVariableQuery"}, "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "barman-exporter", "value": "barman-exporter"}, "datasource": null, "definition": "label_values(node_boot_time_seconds,job)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Job", "multi": false, "name": "Job", "options": [{"selected": false, "text": "node-exporter", "value": "node-exporter"}, {"selected": false, "text": "ews-tableau", "value": "ews-tableau"}, {"selected": false, "text": "redis-acc-all", "value": "redis-acc-all"}, {"selected": false, "text": "redis-bet-all", "value": "redis-bet-all"}, {"selected": false, "text": "redis-gam-all", "value": "redis-gam-all"}, {"selected": false, "text": "redis-gen-all", "value": "redis-gen-all"}, {"selected": false, "text": "redis-sport-all", "value": "redis-sport-all"}, {"selected": false, "text": "redis-pay-all", "value": "redis-pay-all"}, {"selected": false, "text": "redis-cache", "value": "redis-cache"}, {"selected": false, "text": "scylla-db-hosts-sm", "value": "scylla-db-hosts-sm"}, {"selected": false, "text": "scylla-manager-sm", "value": "scylla-manager-sm"}, {"selected": false, "text": "postgres-sm", "value": "postgres-sm"}, {"selected": true, "text": "barman-exporter", "value": "barman-exporter"}, {"selected": false, "text": "redis-bi-all", "value": "redis-bi-all"}, {"selected": false, "text": "clickhouse-sm-sba", "value": "clickhouse-sm-sba"}, {"selected": false, "text": "coredns-vlan", "value": "coredns-vlan"}, {"selected": false, "text": "rabbitmq-vms-metrics-svc", "value": "rabbitmq-vms-metrics-svc"}, {"selected": false, "text": "rng", "value": "rng"}, {"selected": false, "text": "sftp-backup-clickhouse-sm", "value": "sftp-backup-clickhouse-sm"}, {"selected": false, "text": "clickhouse-ds", "value": "clickhouse-ds"}, {"selected": false, "text": "artifactory-node-exporter", "value": "artifactory-node-exporter"}], "query": {"query": "label_values(node_boot_time_seconds,job)", "refId": "StandardVariableQuery"}, "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": "************:9100", "value": "************:9100"}, "datasource": null, "definition": "label_values(node_time_seconds{job=\"barman-exporter\"},instance)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Host", "multi": false, "name": "host", "options": [], "query": {"query": "label_values(node_time_seconds{job=\"barman-exporter\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-2d", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Barman", "uid": "Jb2lonIWka", "version": 3}