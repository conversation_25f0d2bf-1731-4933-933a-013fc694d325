{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 4907, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 22, "panels": [], "title": "General", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 2, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "system_cpu_usage{instance=\"$instance\"}", "interval": "", "legendFormat": "System CPU Usage", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "process_cpu_usage{instance=\"$instance\"}", "hide": false, "interval": "", "legendFormat": "Process CPU Usage", "refId": "B"}], "title": "CPU Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 20, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "system_load_average_1m{instance=\"$instance\"}", "interval": "", "legendFormat": "Load Average [1m]", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "system_cpu_count{instance=\"$instance\"}", "hide": false, "interval": "", "legendFormat": "CPU Core Size", "refId": "B"}], "title": "Load Average", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 0, "y": 9}, "id": 39, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_uptime_seconds{instance=\"$instance\"}", "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Uptime", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "dateTimeAsIso"}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 12, "y": 9}, "id": 40, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_start_time_seconds{instance=\"$instance\"}*1000", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Start time", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 80}, {"color": "dark-red", "value": 90}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 13}, "id": 6, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto", "text": {}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(jvm_memory_used_bytes{instance=\"$instance\", area=\"heap\"})*100/sum(jvm_memory_max_bytes{instance=\"$instance\", area=\"heap\"})", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Heap Used", "type": "gauge"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 80}, {"color": "dark-red", "value": 90}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 13}, "id": 8, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto", "text": {}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(jvm_memory_used_bytes{instance=\"$instance\", area=\"nonheap\"})*100/sum(jvm_memory_max_bytes{instance=\"$instance\", area=\"nonheap\"})", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Non-Heap Used", "type": "gauge"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 19}, "id": 42, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "jvm_memory_used_bytes{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "jvm_memory_used_bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 19}, "id": 44, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "jvm_threads_daemon_threads{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "jvm_threads_daemon_threads", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 27}, "id": 14, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "http_server_requests_seconds_count{instance=\"$instance\", uri!~\".*actuator.*\"}", "interval": "", "legendFormat": "{{method}} [{{status}}] - {{uri}}", "refId": "A"}], "title": "Request Count", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 37}, "id": 46, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "hikaricp_connections_idle{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "hikaricp_connections_idle", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 37}, "id": 48, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "hikaricp_connections_creation_seconds_max{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "hikaricp_connections_creation_seconds_max", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 45}, "id": 50, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "hikaricp_connections_creation_seconds_count{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "hikaricp_connections_creation_seconds_count", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 45}, "id": 52, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "hikaricp_connections_creation_seconds_sum{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "hikaricp_connections_creation_seconds_sum", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 53}, "id": 54, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "trigger_handler_error_total{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Market template handler errors", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 53}, "id": 56, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "trigger_kafka_producer_error_total{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Kafka producer errors for settlement builder", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 61}, "id": 58, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "trigger_kafka_cancel_timeframe_error_total{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Cancel by timeframe errors", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 61}, "id": 60, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "trigger_alerts_kafka_producer_error_total{instance=\"$instance\"}", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Kafka producer errors for alerts batching", "type": "timeseries"}], "preload": false, "refresh": "5s", "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "", "value": ""}, "label": "Feed Type", "name": "feed_type", "options": [{"selected": true, "text": "Premium", "value": ""}, {"selected": false, "text": "Standard", "value": "standard-"}], "query": "Premium :   ,Standard : standard-", "type": "custom"}, {"current": {"text": "ews-settlement-trigger", "value": "ews-settlement-trigger"}, "datasource": {"type": "prometheus"}, "definition": "label_values(jvm_classes_loaded_classes{}, job)", "includeAll": false, "label": "Job", "name": "job", "options": [], "query": {"query": "label_values(jvm_classes_loaded_classes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/^${feed_type}ews-settlement-trigger.*/", "type": "query"}, {"current": {"text": "ews-dev", "value": "ews-dev"}, "datasource": {"type": "prometheus"}, "definition": "label_values(jvm_classes_loaded_classes{job=\"$job\"}, namespace)", "includeAll": false, "label": "Namespace", "name": "namespace", "options": [], "query": {"query": "label_values(jvm_classes_loaded_classes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "172.17.51.245:80", "value": "172.17.51.245:80"}, "datasource": {"type": "prometheus"}, "definition": "label_values(jvm_classes_loaded_classes{job=\"$job\", namespace=\"$namespace\"},instance)", "includeAll": false, "label": "Instance", "name": "instance", "options": [], "query": {"query": "label_values(jvm_classes_loaded_classes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Settlement Trigger", "uid": "HYnBdgt7kedfrdjfhd", "version": 1, "weekStart": ""}