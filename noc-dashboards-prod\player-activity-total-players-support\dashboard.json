{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 368, "links": [], "panels": [{"datasource": "audit-logs-monitoring-db", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}}, "overrides": []}, "gridPos": {"h": 3, "w": 8, "x": 0, "y": 0}, "id": 14, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {"valueSize": 40}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select\n    sum(count_id)\nFrom monitoring_player_registrations\nwhere $__timeFilter(create_date)", "refId": "B", "select": [[{"params": ["redis_wait"], "type": "column"}]], "table": "sport_offering_timing", "timeColumn": "date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "timeFrom": null, "timeShift": null, "title": "New registrations", "type": "stat"}, {"datasource": "audit-logs-monitoring-db", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}}, "overrides": []}, "gridPos": {"h": 3, "w": 8, "x": 8, "y": 0}, "id": 20, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  activity_time AS \"time\",\n  sum(player_count) AS \"Total\"\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time)\nGROUP BY 1\nORDER BY 1", "refId": "B", "select": [[{"params": ["redis_wait"], "type": "column"}]], "table": "sport_offering_timing", "timeColumn": "date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "timeFrom": null, "timeShift": null, "title": "Active players", "type": "stat"}, {"datasource": "audit-logs-monitoring-db", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}}, "overrides": []}, "gridPos": {"h": 3, "w": 8, "x": 16, "y": 0}, "id": 17, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["max"], "fields": "", "values": false}, "text": {"valueSize": 40}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  activity_time AS \"time\",\n  sum(player_count) AS \"Total\"\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time)\nGROUP BY 1\nORDER BY 1", "refId": "B", "select": [[{"params": ["redis_wait"], "type": "column"}]], "table": "sport_offering_timing", "timeColumn": "date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "timeFrom": null, "timeShift": null, "title": "Maximum active players", "type": "stat"}, {"datasource": "PostgreSQL DWH", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "0"}, "properties": [{"id": "displayName", "value": "Sport"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "1"}, "properties": [{"id": "displayName", "value": "EGT Interactive"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "10"}, "properties": [{"id": "displayName", "value": "NetEnt"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "11"}, "properties": [{"id": "displayName", "value": "Amatic"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "12"}, "properties": [{"id": "displayName", "value": "Casino Technology (direct)"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "14"}, "properties": [{"id": "displayName", "value": "Habanero"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "2"}, "properties": [{"id": "displayName", "value": "EGT Digital Games"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "22"}, "properties": [{"id": "displayName", "value": "Scientific Games Digital"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "3"}, "properties": [{"id": "displayName", "value": "Evolution"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "4"}, "properties": [{"id": "displayName", "value": "<PERSON><PERSON>"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "41"}, "properties": [{"id": "displayName", "value": "7Mojos"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "6"}, "properties": [{"id": "displayName", "value": "Spribe"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "7"}, "properties": [{"id": "displayName", "value": "Evoplay"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "8"}, "properties": [{"id": "displayName", "value": "Pragmatic"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "36"}, "properties": [{"id": "displayName", "value": "Playtech"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "42"}, "properties": [{"id": "displayName", "value": "Red Tiger"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "43"}, "properties": [{"id": "displayName", "value": "NetEnt"}]}]}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 3}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {"valueSize": 40}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"format": "table", "group": [], "metricColumn": "business_unit", "rawQuery": true, "rawSql": "select count(distinct player_id)\nfrom reporting.sport_player_dimension_aggregation\nwhere $__timeFilter(updated_date) and $__timeFilter(date::date)", "refId": "A", "select": [[{"params": ["redis_wait"], "type": "column"}]], "table": "sport_offering_timing", "timeColumn": "date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "timeFrom": null, "timeShift": null, "title": "Unique users with bets per minute, SPORT", "transformations": [], "type": "stat"}, {"datasource": "PostgreSQL DWH", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "0"}, "properties": [{"id": "displayName", "value": "Sport"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "1"}, "properties": [{"id": "displayName", "value": "EGT Interactive"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "10"}, "properties": [{"id": "displayName", "value": "NetEnt"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "11"}, "properties": [{"id": "displayName", "value": "Amatic"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "12"}, "properties": [{"id": "displayName", "value": "Casino Technology (direct)"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "14"}, "properties": [{"id": "displayName", "value": "Habanero"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "2"}, "properties": [{"id": "displayName", "value": "EGT Digital Games"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "22"}, "properties": [{"id": "displayName", "value": "Scientific Games Digital"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "3"}, "properties": [{"id": "displayName", "value": "Evolution"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "4"}, "properties": [{"id": "displayName", "value": "<PERSON><PERSON>"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "41"}, "properties": [{"id": "displayName", "value": "7Mojos"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "6"}, "properties": [{"id": "displayName", "value": "Spribe"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "7"}, "properties": [{"id": "displayName", "value": "Evoplay"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "8"}, "properties": [{"id": "displayName", "value": "Pragmatic"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "36"}, "properties": [{"id": "displayName", "value": "Playtech"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "42"}, "properties": [{"id": "displayName", "value": "Red Tiger"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "43"}, "properties": [{"id": "displayName", "value": "NetEnt"}]}]}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 3}, "id": 6, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {"valueSize": 40}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"format": "table", "group": [], "metricColumn": "business_unit", "rawQuery": true, "rawSql": "select count(distinct player_id)\nfrom reporting.casino_player_dimension_aggregation\nwhere $__timeFilter(updated_date) and not is_live and $__timeFilter(date::date)", "refId": "A", "select": [[{"params": ["player_count"], "type": "column"}]], "table": "player_activity", "timeColumn": "activity_time", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "timeFrom": null, "timeShift": null, "title": "Unique users with bets per minute, CASINO", "transformations": [], "type": "stat"}, {"datasource": "PostgreSQL DWH", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "0"}, "properties": [{"id": "displayName", "value": "Sport"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "1"}, "properties": [{"id": "displayName", "value": "EGT Interactive"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "10"}, "properties": [{"id": "displayName", "value": "NetEnt"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "11"}, "properties": [{"id": "displayName", "value": "Amatic"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "12"}, "properties": [{"id": "displayName", "value": "Casino Technology (direct)"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "14"}, "properties": [{"id": "displayName", "value": "Habanero"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "2"}, "properties": [{"id": "displayName", "value": "EGT Digital Games"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "22"}, "properties": [{"id": "displayName", "value": "Scientific Games Digital"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "3"}, "properties": [{"id": "displayName", "value": "Evolution"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "4"}, "properties": [{"id": "displayName", "value": "<PERSON><PERSON>"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "41"}, "properties": [{"id": "displayName", "value": "7Mojos"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "6"}, "properties": [{"id": "displayName", "value": "Spribe"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "7"}, "properties": [{"id": "displayName", "value": "Evoplay"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "8"}, "properties": [{"id": "displayName", "value": "Pragmatic"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "36"}, "properties": [{"id": "displayName", "value": "Playtech"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "42"}, "properties": [{"id": "displayName", "value": "Red Tiger"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "43"}, "properties": [{"id": "displayName", "value": "NetEnt"}]}]}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 3}, "id": 24, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {"valueSize": 40}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"format": "table", "group": [], "metricColumn": "business_unit", "rawQuery": true, "rawSql": "select count(distinct player_id)\nfrom reporting.casino_player_dimension_aggregation\nwhere $__timeFilter(updated_date) and is_live and $__timeFilter(date::date)", "refId": "A", "select": [[{"params": ["player_count"], "type": "column"}]], "table": "player_activity", "timeColumn": "activity_time", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "timeFrom": null, "timeShift": null, "title": "Unique users with bets per minute, Live CASINO", "transformations": [], "type": "stat"}, {"datasource": "PostgreSQL DWH", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 3}, "id": 8, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {"valueSize": 40}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"format": "table", "group": [], "metricColumn": "business_unit", "rawQuery": true, "rawSql": "with abc as (select distinct player_id\n             from reporting.casino_player_dimension_aggregation\n             where $__timeFilter(updated_date) and $__timeFilter(date::date)\n                union\n             select distinct player_id\n             from reporting.sport_player_dimension_aggregation\n             where $__timeFilter(updated_date) and $__timeFilter(date::date)\n            ) select count(*) from abc;", "refId": "A", "select": [[{"params": ["player_count"], "type": "column"}]], "table": "player_activity", "timeColumn": "activity_time", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "timeFrom": null, "timeShift": null, "title": "Unique users with bets per minute, Total Players", "transformations": [], "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "audit-logs-monitoring-db", "fieldConfig": {"defaults": {"custom": {}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "1"}, "properties": [{"id": "displayName", "value": "EGT Interactive"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "10"}, "properties": [{"id": "displayName", "value": "NetEnt"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "11"}, "properties": [{"id": "displayName", "value": "Amatic"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "12"}, "properties": [{"id": "displayName", "value": "Casino Technology (direct)"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "14"}, "properties": [{"id": "displayName", "value": "Habanero"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "2"}, "properties": [{"id": "displayName", "value": "EGT Digital Games"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "22"}, "properties": [{"id": "displayName", "value": "Scientific Games Digital"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "3"}, "properties": [{"id": "displayName", "value": "Evolution"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "36"}, "properties": [{"id": "displayName", "value": "Playtech"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "4"}, "properties": [{"id": "displayName", "value": "<PERSON><PERSON>"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "41"}, "properties": [{"id": "displayName", "value": "7Mojos"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "42"}, "properties": [{"id": "displayName", "value": "Red Tiger"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "43"}, "properties": [{"id": "displayName", "value": "NetEnt"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "6"}, "properties": [{"id": "displayName", "value": "Spribe"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "7"}, "properties": [{"id": "displayName", "value": "Evoplay"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "8"}, "properties": [{"id": "displayName", "value": "Pragmatic"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 6}, "hiddenSeries": false, "id": 12, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  activity_time AS \"time\",\n  sum(player_count) AS \"Casino players\"\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id != 0\nGROUP BY 1\nORDER BY 1", "refId": "B", "select": [[{"params": ["redis_wait"], "type": "column"}]], "table": "sport_offering_timing", "timeColumn": "date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Unique users with bets per minute, CASINO", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2401", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:2402", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "audit-logs-monitoring-db", "fieldConfig": {"defaults": {"custom": {}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "0"}, "properties": [{"id": "displayName", "value": "Sport"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 14}, "hiddenSeries": false, "id": 23, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "Sport", "yaxis": 1}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  activity_time AS \"time\",\n  sum(player_count) AS \"Sport Total\"\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id = 0\nGROUP BY 1\nORDER BY 1", "refId": "A", "select": [[{"params": ["redis_wait"], "type": "column"}]], "table": "sport_offering_timing", "timeColumn": "date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Unique users with bets per minute, SPORT", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2401", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:2402", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "5m", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Player Activity - Total count players (support)", "uid": "y6Q0XaU712ihbguhebigf-support", "version": 2}