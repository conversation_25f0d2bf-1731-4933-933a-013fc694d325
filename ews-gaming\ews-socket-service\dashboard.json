{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 314, "iteration": 1623673261440, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 10, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "subscribe_event_zset_loaded_count{instance=\"$instance\" }", "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "ZSet loaded records", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "hiddenSeries": false, "id": 6, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(subscribe_event_zset_load_seconds_sum{instance=\"$instance\" }[1m])/rate(subscribe_event_zset_load_seconds_count{instance=\"$instance\"}[1m])", "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "ZSet load time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "hiddenSeries": false, "id": 8, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(subscribe_event_zset_save_seconds_sum{instance=\"$instance\" }[1m])/rate(subscribe_event_zset_save_seconds_count{instance=\"$instance\"}[1m])", "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "ZSet save time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 13, "w": 24, "x": 0, "y": 16}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": false, "expr": "rate(http_client_requests_seconds_sum{clientName=\"rabbitmq-haproxy-clusterip\",outcome=\"SUCCESS\", instance=\"$instance\" }[1m])/rate(http_client_requests_seconds_count{clientName=\"rabbitmq-haproxy-clusterip\",  outcome=\"SUCCESS\",instance=\"$instance\"}[1m])", "interval": "", "legendFormat": "method: {{method}}, status: {{status}},  uri: {{uri}}", "refId": "A"}, {"hide": false, "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Calls to <PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"color": {}, "custom": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 29}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(http_server_requests_seconds_sum{instance=\"$instance\", uri=\"/api/websocket/public/routings\" }[1m])/rate(http_server_requests_seconds_count{instance=\"$instance\",uri=\"/api/websocket/public/routings\"}[1m])", "interval": "", "legendFormat": "method: {{method}}, status: {{status}},  uri: {{uri}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "API calls", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "ews-sof-prod", "value": "ews-sof-prod"}, "datasource": null, "definition": "label_values(jvm_classes_loaded_classes{job=\"$job\"}, namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Namespace", "multi": false, "name": "namespace", "options": [{"selected": true, "text": "ews-sof-prod", "value": "ews-sof-prod"}], "query": {"query": "label_values(jvm_classes_loaded_classes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-websocket-mgnt", "value": "ews-websocket-mgnt"}, "datasource": null, "definition": "label_values(jvm_classes_loaded_classes{}, job)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Job", "multi": false, "name": "job", "options": [{"selected": true, "text": "ews-websocket-mgnt", "value": "ews-websocket-mgnt"}, {"selected": false, "text": "ews-websocket-mgnt-01", "value": "ews-websocket-mgnt-01"}, {"selected": false, "text": "ews-websocket-mgnt-02", "value": "ews-websocket-mgnt-02"}, {"selected": false, "text": "ews-websocket-mgnt-03", "value": "ews-websocket-mgnt-03"}, {"selected": false, "text": "ews-websocket-mgnt-04", "value": "ews-websocket-mgnt-04"}], "query": {"query": "label_values(jvm_classes_loaded_classes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 0, "regex": "/ews-websocket-.*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "***********:8891", "value": "***********:8891"}, "datasource": null, "definition": "label_values(jvm_classes_loaded_classes{job=\"$job\", namespace=\"$namespace\"},instance)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Instance", "multi": false, "name": "instance", "options": [{"selected": false, "text": "**********:8891", "value": "**********:8891"}, {"selected": true, "text": "***********:8891", "value": "***********:8891"}], "query": {"query": "label_values(jvm_classes_loaded_classes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "SOCKET SERVICE", "uid": "yRAyVgg7zfet", "version": 6}