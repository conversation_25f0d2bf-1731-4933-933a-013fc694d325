{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": false, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}, {"class": "annotation_restart", "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "enable": true, "expr": "resets(scylla_gossip_heart_beat{cluster=\"$cluster\"}[$__rate_interval])>0", "hide": false, "iconColor": "rgba(255, 96, 96, 1)", "limit": 100, "name": "node_restart", "showIn": 0, "tagKeys": "instance,dc,cluster", "tags": [], "titleFormat": "restart", "type": "tags"}, {"class": "annotation_stall", "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "enable": false, "expr": "changes(scylla_stall_detector_reported{cluster=\"$cluster\"}[$__rate_interval])>0", "hide": false, "iconColor": "rgba(255, 96, 96, 1)", "limit": 100, "name": "stall detector", "showIn": 0, "tagKeys": "dc,instance,shard", "tags": [], "titleFormat": "<PERSON><PERSON> found", "type": "tags"}, {"class": "annotation_schema_changed", "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "enable": false, "expr": "changes(scylla_database_schema_changed{cluster=\"$cluster\"}[$__rate_interval])>0", "hide": false, "iconColor": "rgba(255, 96, 96, 1)", "limit": 100, "name": "<PERSON><PERSON><PERSON> Changed", "showIn": 0, "tagKeys": "instance,dc,cluster", "tags": [], "titleFormat": "schema changed", "type": "tags"}, {"class": "annotation_manager_task", "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "enable": true, "expr": "scylla_manager_task_active_count{type=~\"repair|backup\",cluster=\"$cluster\"}>0", "hide": false, "iconColor": "#73BF69", "limit": 100, "name": "Task", "showIn": 0, "tagKeys": "type", "tags": [], "titleFormat": "Running", "type": "tags"}, {"class": "annotation_hints_writes", "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "enable": false, "expr": "changes(scylla_hints_manager_written{cluster=\"$cluster\"}[$__rate_interval])>0", "hide": false, "iconColor": "rgb(255, 176, 0, 128)", "limit": 100, "name": "Hints Write", "showIn": 0, "tagKeys": "instance,dc,cluster", "tags": [], "titleFormat": "Hints write", "type": "tags"}, {"class": "annotation_hints_sent", "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "enable": false, "expr": "changes(scylla_hints_manager_sent{cluster=\"$cluster\"}[$__rate_interval])>0", "hide": false, "iconColor": "rgb(50, 176, 0, 128)", "limit": 100, "name": "<PERSON><PERSON>", "showIn": 0, "tagKeys": "instance,dc,cluster", "tags": [], "titleFormat": "<PERSON><PERSON>", "type": "tags"}, {"class": "mv_building", "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "enable": true, "expr": "sum(scylla_view_builder_builds_in_progress{cluster=\"$cluster\"})>0", "hide": false, "iconColor": "rgb(50, 176, 0, 128)", "limit": 100, "name": "MV", "showIn": 0, "tagKeys": "instance,dc,cluster", "tags": [], "titleFormat": "Materialized View built", "type": "tags"}, {"class": "ops_annotation", "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "enable": true, "expr": "10*min(scylla_node_ops_finished_percentage{cluster=\"$cluster\"}) by (ops, dc,instance) < 10", "hide": false, "iconColor": "rgb(50, 176, 0, 128)", "limit": 100, "name": "ops", "showIn": 0, "tagKeys": "ops,dc,instance", "tags": [], "titleFormat": "Operation", "type": "tags"}, {"class": "stream_annotation", "dashversion": [">5.2", ">2023.1"], "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "enable": true, "expr": "10*min(scylla_streaming_finished_percentage{cluster=\"$cluster\"}) by (ops, dc,instance) < 10", "hide": false, "iconColor": "rgb(50, 176, 0, 128)", "limit": 100, "name": "streaming", "showIn": 0, "tagKeys": "ops,dc,instance", "tags": [], "titleFormat": "Streaming", "type": "tags"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": 4325, "links": [{"asDropdown": true, "icon": "external link", "includeVars": true, "keepTime": true, "tags": [], "type": "dashboards"}], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 1, "panels": [], "title": "Cluster overview $cluster", "type": "row"}, {"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 1, "w": 9, "x": 0, "y": 1}, "id": 2, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "<img src=\"https://repositories.scylladb.com/scylla/imgversion/$monitoring_version/scylla-monitoring\">", "mode": "html"}, "pluginVersion": "11.3.0", "title": "", "type": "text"}, {"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 1, "w": 4, "x": 9, "y": 1}, "id": 3, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "# ", "mode": "markdown"}, "pluginVersion": "11.3.0", "title": "", "type": "text"}, {"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 1, "w": 4, "x": 13, "y": 1}, "id": 4, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "# ", "mode": "markdown"}, "pluginVersion": "11.3.0", "title": "", "type": "text"}, {"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 1, "w": 7, "x": 17, "y": 1}, "id": 5, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "# ", "mode": "markdown"}, "pluginVersion": "11.3.0", "title": "", "type": "text"}, {"datasource": {"type": "prometheus"}, "description": "The number of nodes configured in the cluster.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 0, "y": 2}, "id": 6, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "count(scylla_scylladb_current_version{job=\"scylla-db-scylla-exporter-sc\", cluster=\"$cluster\"})", "interval": "", "intervalFactor": 1, "legendFormat": "Total Nodes", "range": true, "refId": "A", "step": 40}], "title": "# Nodes", "type": "stat"}, {"datasource": {"type": "prometheus"}, "description": "The number of unreachable nodes.\nUsually because a machine is down or unreachable.", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "green", "value": 0}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 2, "y": 2}, "id": 7, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "(count(scrape_samples_scraped{job=\"scylla\", cluster=\"$cluster\"}==0) OR vector(0))", "intervalFactor": 1, "legendFormat": "Offline ", "refId": "A", "step": 20}], "title": "Unreachable", "type": "stat"}, {"datasource": {"type": "prometheus"}, "description": "The number of joining and leaving nodes.\nThe number of nodes that are up but not actively part of the cluster, either because they are still joining or because they are leaving.", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "green", "value": 0}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 4, "y": 2}, "id": 8, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "count(scylla_node_operation_mode{cluster=\"$cluster\"}!=3)OR vector(0)", "intervalFactor": 1, "legendFormat": "Offline ", "refId": "A", "step": 20}], "title": "Inactive", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"mappings": [], "noValue": " Offline", "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 0}]}}, "overrides": [{"matcher": {"id": "byFrameRefID", "options": "A"}, "properties": [{"id": "mappings", "value": [{"options": {"0": {"text": "Online"}, "1": {"text": "Repair"}, "2": {"text": "Backup"}, "3": {"text": "Backup Repair"}, "-1": {"text": "Offline"}}, "type": "value"}]}]}, {"matcher": {"id": "byFrameRefID", "options": "B"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "mappings", "value": [{"options": {"0": {"text": " "}}, "type": "value"}]}]}]}, "gridPos": {"h": 4, "w": 3, "x": 6, "y": 2}, "id": 9, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "value", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "(max(scylla_manager_scheduler_run_indicator{type=~\"repair\",cluster=\"$cluster\"}) or on() vector(0)) + (max(scylla_manager_scheduler_run_indicator{type=~\"backup\",cluster=\"$cluster\"})*2 or on() vector(0)) + (sum(scylla_manager_server_current_version{}) or on() vector(-1))", "intervalFactor": 1, "refId": "A", "step": 40}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "(avg(scylla_manager_repair_progress{cluster=\"$cluster\", job=\"scylla_manager\"}) * max(scylla_manager_scheduler_run_indicator{type=\"repair\",cluster=\"$cluster\"})) or on ()  (100*avg(manager:backup_progress{cluster=\"$cluster\"}) * max(scylla_manager_scheduler_run_indicator{type=\"backup\",cluster=\"$cluster\"})) or on () vector(0)", "instant": true, "interval": "", "legendFormat": "", "refId": "B"}], "title": "Manager", "type": "stat"}, {"datasource": {"type": "prometheus"}, "description": "Average Write Latency", "fieldConfig": {"defaults": {"decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 50000}]}, "unit": "Âµs"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 9, "y": 2}, "id": 10, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "avg(wlatencya{by=\"cluster\", cluster=~\"$cluster|^$\",scheduling_group_name=~\"$sg\"}>0)", "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 4}], "title": "Avg Write", "type": "stat"}, {"datasource": {"type": "prometheus"}, "description": "99% write Latency", "fieldConfig": {"defaults": {"decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 100000}]}, "unit": "Âµs"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 11, "y": 2}, "id": 11, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "wlatencyp99{by=\"cluster\", cluster=~\"$cluster|^$\",scheduling_group_name=~\"$sg\"}>0", "instant": true, "intervalFactor": 1, "legendFormat": "{{scheduling_group_name}}", "refId": "A", "step": 4}], "title": "99% Write", "type": "stat"}, {"datasource": {"type": "prometheus"}, "description": "Average Read Latency", "fieldConfig": {"defaults": {"decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 50000}]}, "unit": "Âµs"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 13, "y": 2}, "id": 12, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "avg(rlatencya{by=\"cluster\", cluster=~\"$cluster|^$\",scheduling_group_name=~\"$sg\"}>0)", "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 4}], "title": "Avg <PERSON>", "type": "stat"}, {"datasource": {"type": "prometheus"}, "description": "99% read Latency", "fieldConfig": {"defaults": {"decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 100000}]}, "unit": "Âµs"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 15, "y": 2}, "id": 13, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "rlatencyp99{by=\"cluster\", cluster=~\"$cluster|^$\",scheduling_group_name=~\"$sg\"}>0", "instant": true, "intervalFactor": 1, "legendFormat": "{{scheduling_group_name}}", "refId": "A", "step": 4}], "title": "99% Read", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"decimals": 1, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "si:"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 17, "y": 2}, "id": 14, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(scylla_transport_requests_served{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) + (sum(rate(scylla_thrift_served{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) or on() vector(0))", "instant": true, "intervalFactor": 1, "refId": "A", "step": 40}], "title": "Requests/s", "type": "stat"}, {"datasource": {"type": "prometheus"}, "description": "The percentage of the time during which <PERSON><PERSON><PERSON> utilized the CPU. Note that because <PERSON><PERSON><PERSON> does busy polling for some time before going idle, CPU utilization as seen by the operating system may be much higher. Your system is not yet CPU-bottlenecked until this metric is high.", "fieldConfig": {"defaults": {"decimals": 0, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 20, "y": 2}, "id": 15, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "avg(scylla_reactor_utilization{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} )", "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 4}], "title": "Load", "type": "stat"}, {"datasource": {"type": "prometheus"}, "description": "The rate of timeouts (read and write).\n\nTimeouts are an indication of an overloaded system", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 22, "y": 2}, "id": 16, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "(sum(rate(scylla_storage_proxy_coordinator_write_timeouts{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])) or on() vector(0)) + (sum(rate(scylla_storage_proxy_coordinator_read_timeouts{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])) or on() vector(0))", "instant": true, "intervalFactor": 1, "refId": "A", "step": 40}], "title": "Timeouts", "type": "stat"}, {"datasource": {"type": "prometheus"}, "description": "Write attempts - include all writes that reached the coordinator node, even if they will eventually fail", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "si:ops/s"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 6}, "id": 17, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "$func(rate(scylla_storage_proxy_coordinator_write_latency_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) or on ([[by]]) $func(rate(scylla_storage_proxy_coordinator_write_latency_summary_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval]))", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 1}], "title": "Writes", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Âµs"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 6, "y": 6}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "wlatencyp95{by=\"cluster\", cluster=\"$cluster\",scheduling_group_name=~\"$sg\"}>0", "intervalFactor": 1, "legendFormat": "{{scheduling_group_name}} 95%", "refId": "A", "step": 1}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "wlatencyp99{by=\"cluster\", cluster=\"$cluster\",scheduling_group_name=~\"$sg\"}>0", "intervalFactor": 1, "legendFormat": "{{scheduling_group_name}} 99%", "refId": "B", "step": 1}], "title": "Write Latencies", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Read attempts - include all reads that reached the coordinator node, even if they will eventually fail", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "si:ops/s"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 12, "y": 6}, "id": 19, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "$func(rate(scylla_storage_proxy_coordinator_read_latency_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) or on ([[by]]) $func(rate(scylla_storage_proxy_coordinator_read_latency_summary_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval]))", "intervalFactor": 1, "legendFormat": "Reads", "refId": "A", "step": 1}], "title": "Reads", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Âµs"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 18, "y": 6}, "id": 20, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "rlatencyp95{by=\"cluster\", cluster=\"$cluster\",scheduling_group_name=~\"$sg\"}>0", "intervalFactor": 1, "legendFormat": "{{scheduling_group_name}} {{instance}} {{shard}} 95%", "refId": "A", "step": 1}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "rlatencyp99{by=\"cluster\", cluster=\"$cluster\",scheduling_group_name=~\"$sg\"}>0", "intervalFactor": 1, "legendFormat": "{{scheduling_group_name}} 99%", "refId": "B", "step": 1}], "title": "Read Latencies", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 12}, "id": 21, "panels": [], "repeat": "dc", "title": "", "type": "row"}, {"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 2, "w": 24, "x": 0, "y": 13}, "id": 22, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "<h1 style=\"color:#5780C1; border-bottom: 3px solid #5780C1;\">Information for $dc</h1>", "mode": "html"}, "pluginVersion": "11.3.0", "title": "", "transparent": true, "type": "text"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"decimals": 0, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 85}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 1, "x": 0, "y": 15}, "id": 23, "options": {"displayMode": "lcd", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 16, "minVizWidth": 8, "namePlacement": "auto", "orientation": "vertical", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "avg(scylla_reactor_utilization{instance=~\"[[node]]\",cluster=~\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"} )", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Load", "type": "bargauge"}, {"datasource": {"type": "prometheus"}, "description": "The average Disk usage per [[by]].\n\n The dashed line represent the total size.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "byFrameRefID", "options": "B"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.lineWidth", "value": 2}]}]}, "gridPos": {"h": 6, "w": 3, "x": 1, "y": 15}, "id": 24, "options": {"class": "desc_tooltip_options", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "Avg(node_filesystem_size_bytes{mountpoint=\"$mount_point\", job=\"scylla-db-hosts-sm\", instance=~\"$node\"}) by ([[by]])-avg(node_filesystem_avail_bytes{mountpoint=\"$mount_point\",  job=\"scylla-db-hosts-sm\", instance=~\"$node\"}) by ([[by]])", "interval": "", "intervalFactor": 1, "legendFormat": "Avg Usage {{instance}} {{shard}}", "metric": "", "refId": "A", "step": 1}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "avg(node_filesystem_size_bytes{mountpoint=\"$mount_point\",job=\"scylla-db-hosts-sm\", instance=~\"$node\"}) by ([[by]])", "interval": "", "legendFormat": "Size {{instance}} {{shard}}", "refId": "B"}], "title": "Average Disk Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Holds the number of currently active compactions.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 4, "y": 15}, "id": 25, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "$func(scylla_compaction_manager_compactions{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}) by ([[by]])", "intervalFactor": 1, "legendFormat": "", "metric": "", "refId": "A", "step": 1}], "title": "Running Compactions", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "The <PERSON> and Misses", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "si:ops/s"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 8, "y": 15}, "id": 26, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "$func(rate(scylla_cache_row_hits{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])", "intervalFactor": 1, "legendFormat": "Hit {{instance}} {{shard}}", "refId": "A", "step": 10}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "$func(rate(scylla_cache_row_misses{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])", "intervalFactor": 1, "legendFormat": "Misses {{instance}} {{shard}}", "refId": "B", "step": 10}], "title": "Cache Hits/Misses", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 85}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "lcd", "type": "gauge"}}, {"id": "min", "value": 0}, {"id": "max", "value": 101}, {"id": "displayName", "value": "Load"}, {"id": "custom.width", "value": 120}, {"id": "decimals", "value": 0}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "service"}, "properties": [{"id": "custom.width", "value": 120}, {"id": "links", "value": [{"title": "Detailed View", "url": "/d/detailed-[[dash_version]]/detailed?refresh=30s&orgId=1&var-by=instance&var-node=${__data.fields[0]}&from=${__from}&to=${__to}"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "custom.width", "value": 105}, {"id": "displayName", "value": "Status"}, {"id": "custom.cellOptions", "value": {"type": "color-text"}}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "yellow", "value": null}, {"color": "blue", "value": 3}, {"color": "red", "value": 4}]}}, {"id": "mappings", "value": [{"options": {"1": {"text": "Starting"}, "2": {"text": "Joining"}, "3": {"text": "Normal"}, "4": {"text": "Leaving"}, "5": {"text": "Decommissioned"}, "6": {"text": "Draining"}, "7": {"text": "Drained"}, "8": {"text": "Moving"}}, "type": "value"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #C"}, "properties": [{"id": "custom.width", "value": 90}, {"id": "mappings", "value": [{"options": {"0": {"text": "OS Info"}}, "type": "value"}, {"options": {"from": 0.001, "result": {"text": "OS Errors"}, "to": 100000}, "type": "range"}]}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "blue", "value": null}, {"color": "orange", "value": 0.001}]}}, {"id": "displayName", "value": "OS"}, {"id": "custom.cellOptions", "value": {"type": "color-text"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #F"}, "properties": [{"id": "custom.width", "value": 80}, {"id": "custom.cellOptions", "value": {"type": "color-text"}}, {"id": "displayName", "value": "Live"}, {"id": "links", "value": [{"title": "How many live nodes this node sees", "url": "/d/detailed-[[dash_version]]/detailed?refresh=30s&orgId=1&var-by=instance&var-node=${__data.fields[0]}&from=${__from}&to=${__to}"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #G"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "lcd", "type": "gauge"}}, {"id": "displayName", "value": "Streaming"}, {"id": "min", "value": 0}, {"id": "max", "value": 100}, {"id": "custom.width", "value": 90}, {"id": "decimals", "value": 0}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "version"}, "properties": [{"id": "custom.width", "value": 90}, {"id": "displayName", "value": "Version"}, {"id": "decimals"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "instance"}, "properties": [{"id": "custom.width", "value": 120}]}]}, "gridPos": {"h": 17, "w": 10, "x": 14, "y": 15}, "id": 27, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "0*scylla_scylladb_current_version{cluster=\"$cluster\", dc=~\"$dc\"} + on (instance) group_left() scylla_node_operation_mode{cluster=\"$cluster\", dc=~\"$dc\"}", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "avg(scylla_reactor_utilization{cluster=~\"$cluster\", dc=~\"$dc\"} ) by (instance)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(scylla_reactor_aio_errors{cluster=~\"$cluster\", dc=~\"$dc\"}[$__rate_interval])) by (instance)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(scylla_errors:nodes_total{cluster=~\"$cluster\", dc=~\"$dc\"}) by (instance) >bool 0", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "(sum(scylla_cql:non_system_prepared1m{cluster=~\"$cluster\", dc=~\"$dc\"}) by (instance) >bool 1) + (sum(rate(scylla_cql_reverse_queries{cluster=~\"$cluster\", dc=~\"$dc\"}[$__rate_interval])) by(instance) >bool 1) + (sum(scylla_cql:non_paged_no_system1m{cluster=~\"$cluster\", dc=~\"$dc\"}) by (instance) >bool 1)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "E"}, {"dashversion": [">5.1", ">2022.2"], "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(scylla_gossip_live{cluster=\"$cluster\"})by (instance)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "F"}, {"dashversion": [">5.2", ">2023.1"], "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "(min(scylla_streaming_finished_percentage{cluster=\"$cluster\", dc=~\"$dc\"}*100) by (instance) < 100) or on (instance)  0*sum(scylla_scylladb_current_version{cluster=\"$cluster\", dc=~\"$dc\"}) by (instance)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "G"}], "title": "Nodes", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["instance", "version", "Value #A", "Value #B", "Value #C", "Value #F", "Value #G"]}}}, {"id": "seriesToColumns", "options": {"byField": "instance"}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {"Value #A": 3, "Value #B": 5, "Value #C": 2, "Value #F": 4, "Value #G": 6, "instance": 0, "version": 1}, "renameByName": {"instance": ""}}}], "type": "table"}, {"datasource": {"type": "prometheus"}, "description": "Write attempts - include all writes that reached the coordinator node, even if they will eventually fail", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "si:ops/s"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 21}, "id": 28, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "$func(rate(scylla_storage_proxy_coordinator_write_latency_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or on([[by]]) $func(rate(scylla_storage_proxy_coordinator_write_latency_summary_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]])", "intervalFactor": 1, "legendFormat": "Writes {{instance}} {{shard}}", "refId": "A", "step": 1}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "$func(rate(scylla_storage_proxy_coordinator_write_latency_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval] offset 1d)) or on ([[by]]) $func(rate(scylla_storage_proxy_coordinator_write_latency_summary_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval] offset 1d))", "interval": "", "intervalFactor": 1, "legendFormat": "1 Day Ago", "refId": "B", "step": 1}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "$func(rate(scylla_storage_proxy_coordinator_write_latency_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval] offset 1w)) or on ([[by]]) $func(rate(scylla_storage_proxy_coordinator_write_latency_summary_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval] offset 1w))", "interval": "", "intervalFactor": 1, "legendFormat": "1 Week Ago", "refId": "C", "step": 1}], "title": "Writes", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Âµs"}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 6, "y": 21}, "id": 29, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "avg(wlatencyp95{by=\"[[by]]\", instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}>0) by (scheduling_group_name, [[by]])", "intervalFactor": 1, "legendFormat": "{{scheduling_group_name}} 95% {{instance}} {{shard}}", "refId": "A", "step": 1}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "avg(wlatencyp99{by=\"[[by]]\", instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}>0) by (scheduling_group_name, [[by]])", "intervalFactor": 1, "legendFormat": "{{scheduling_group_name}} 99% {{instance}} {{shard}}", "refId": "B", "step": 1}], "title": "Write Latencies", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Requests that <PERSON><PERSON><PERSON> tried to write but timed out. Timeouts are counted in the node that received the request (the coordinator), not at the replicas.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "si:ops/s"}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 10, "y": 21}, "id": 30, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "$func(rate(scylla_storage_proxy_coordinator_write_timeouts{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\"}[$__rate_interval])) by ([[by]])", "intervalFactor": 1, "legendFormat": "Writes {{instance}} {{shard}}", "refId": "A", "step": 10}], "title": "Write Timeouts by [[by]]", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Read attempts - include all reads that reached the coordinator node, even if they will eventually fail", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "si:ops/s"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 27}, "id": 31, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "$func(rate(scylla_storage_proxy_coordinator_read_latency_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]]) or on([[by]]) $func(rate(scylla_storage_proxy_coordinator_read_latency_summary_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",scheduling_group_name=~\"$sg\"}[$__rate_interval])) by ([[by]])", "intervalFactor": 1, "legendFormat": "Reads {{instance}} {{shard}}", "refId": "A", "step": 1}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "$func(rate(scylla_storage_proxy_coordinator_read_latency_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",scheduling_group_name=~\"$sg\"}[$__rate_interval] offset 1d)) or on ([[by]]) $func(rate(scylla_storage_proxy_coordinator_read_latency_summary_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",scheduling_group_name=~\"$sg\"}[$__rate_interval] offset 1d))", "intervalFactor": 1, "legendFormat": "1 Day Ago", "refId": "B", "step": 1}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "$func(rate(scylla_storage_proxy_coordinator_read_latency_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",scheduling_group_name=~\"$sg\"}[$__rate_interval] offset 1w)) or on ([[by]]) $func(rate(scylla_storage_proxy_coordinator_read_latency_summary_count{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\",scheduling_group_name=~\"$sg\"}[$__rate_interval] offset 1w))", "intervalFactor": 1, "legendFormat": "1 Week Ago", "refId": "C", "step": 1}], "title": "Reads", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Âµs"}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 6, "y": 27}, "id": 32, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "avg(rlatencyp95{by=\"[[by]]\", instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}>0) by(scheduling_group_name, [[by]])", "intervalFactor": 1, "legendFormat": "{{scheduling_group_name}} 95% {{instance}} {{shard}}", "refId": "A", "step": 1}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "avg(rlatencyp99{by=\"[[by]]\", instance=~\"[[node]]|^$\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]|$^\",scheduling_group_name=~\"$sg\"}>0) by(scheduling_group_name, [[by]])", "intervalFactor": 1, "legendFormat": "{{scheduling_group_name}} 99% {{instance}} {{shard}}", "refId": "B", "step": 1}], "title": "Read Latencies", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Requests that <PERSON><PERSON><PERSON> tried to read but timed out. Timeouts are counted in the node that received the request (the coordinator), not at the replicas.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "si:ops/s"}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 10, "y": 27}, "id": 33, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "($func(rate(scylla_storage_proxy_coordinator_read_timeouts{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or vector(0))+on([[by]]) ($func(rate(scylla_storage_proxy_coordinator_cas_read_timeouts{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or vector(0))+on([[by]]) ($func(rate(scylla_storage_proxy_coordinator_range_timeouts{instance=~\"[[node]]\",cluster=\"$cluster\", dc=~\"$dc\", shard=~\"[[shard]]\"}[$__rate_interval])) by ([[by]]) or vector(0))", "intervalFactor": 1, "legendFormat": "Read {{instance}} {{shard}}", "refId": "A", "step": 10}], "title": "Read Timeouts by [[by]]", "type": "timeseries"}, {"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 1, "w": 10, "x": 14, "y": 32}, "id": 34, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "<img src=\"https://repositories.scylladb.com/scylla/imgversion/$all_scyllas_versions/scylla\"></img>", "mode": "html"}, "pluginVersion": "11.3.0", "title": "", "transparent": true, "type": "text"}, {"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 1, "w": 10, "x": 14, "y": 33}, "id": 35, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "", "mode": "html"}, "pluginVersion": "11.3.0", "title": "", "transparent": true, "type": "text"}], "preload": false, "refresh": "30s", "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "instance", "value": "instance"}, "includeAll": false, "label": "by", "name": "by", "options": [{"selected": false, "text": "Cluster", "value": "cluster"}, {"selected": false, "text": "DC", "value": "dc"}, {"selected": true, "text": "Instance", "value": "instance"}, {"selected": false, "text": "<PERSON><PERSON>", "value": "instance,shard"}], "query": "Cluster : cluster,DC : dc, Instance : instance, Shard : instance\\,shard", "type": "custom"}, {"current": {"text": "", "value": ""}, "datasource": {"type": "prometheus"}, "definition": "label_values(scylla_reactor_utilization, cluster)", "includeAll": false, "label": "cluster", "name": "cluster", "options": [], "query": {"query": "label_values(scylla_reactor_utilization, cluster)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "sort": 1, "type": "query"}, {"allValue": ".*", "current": {"text": "All", "value": "$__all"}, "datasource": {"type": "prometheus"}, "definition": "label_values(scylla_reactor_utilization{cluster=~\"$cluster\"}, dc)", "includeAll": true, "label": "dc", "multi": true, "name": "dc", "options": [], "query": {"query": "label_values(scylla_reactor_utilization{cluster=~\"$cluster\"}, dc)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "sort": 1, "type": "query"}, {"allValue": ".*", "current": {"text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus"}, "definition": "label_values(scylla_reactor_utilization{cluster=\"$cluster\", dc=~\"$dc\"}, instance)", "includeAll": true, "label": "node", "multi": true, "name": "node", "options": [], "query": {"query": "label_values(scylla_reactor_utilization{cluster=\"$cluster\", dc=~\"$dc\"}, instance)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "sort": 1, "type": "query"}, {"allValue": ".+", "current": {"text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus"}, "definition": "label_values(scylla_reactor_utilization{cluster=\"$cluster\"},shard)", "includeAll": true, "label": "shard", "multi": true, "name": "shard", "options": [], "query": {"query": "label_values(scylla_reactor_utilization{cluster=\"$cluster\"},shard)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "sort": 3, "type": "query"}, {"current": {"text": "/", "value": "/"}, "datasource": {"type": "prometheus"}, "definition": "node_filesystem_avail_bytes{job=~\"scylla-.*\"}", "includeAll": false, "label": "Mount path", "name": "mount_point", "options": [], "query": {"query": "node_filesystem_avail_bytes{job=~\"scylla-.*\"}", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "/mountpoint=\"([^\"]*)\".*/", "type": "query"}, {"allValue": ".*", "current": {"text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus"}, "definition": "label_values(scylla_scheduler_runtime_ms{cluster=~\"$cluster\", group!~\"atexit|gossip|mem_compaction|memtable|streaming|background_reclaim|compaction|main|memtable_to_cache\"},group)", "includeAll": true, "label": "SG", "multi": true, "name": "sg", "options": [], "query": {"query": "label_values(scylla_scheduler_runtime_ms{cluster=~\"$cluster\", group!~\"atexit|gossip|mem_compaction|memtable|streaming|background_reclaim|compaction|main|memtable_to_cache\"},group)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "sort": 3, "type": "query"}, {"current": {"text": "sum", "value": "sum"}, "includeAll": false, "label": "Function", "name": "func", "options": [{"selected": true, "text": "sum", "value": "sum"}, {"selected": false, "text": "avg", "value": "avg"}, {"selected": false, "text": "max", "value": "max"}, {"selected": false, "text": "min", "value": "min"}, {"selected": false, "text": "stddev", "value": "stddev"}, {"selected": false, "text": "stdvar", "value": "stdvar"}], "query": "sum,avg,max,min,stddev,stdvar", "type": "custom"}, {"current": {"text": "5-4", "value": "5-4"}, "hide": 2, "includeAll": false, "name": "dash_version", "options": [{"selected": true, "text": "5-4", "value": "5-4"}], "query": "5-4", "type": "custom"}, {"allValue": ".*", "current": {"text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus"}, "definition": "label_values(scylla_scylladb_current_version{cluster=\"$cluster\"}, version)", "hide": 2, "includeAll": true, "multi": true, "name": "all_scyllas_versions", "options": [], "query": {"query": "label_values(scylla_scylladb_current_version{cluster=\"$cluster\"}, version)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "sort": 1, "type": "query"}, {"allValue": ".*", "current": {"text": "All", "value": "$__all"}, "datasource": {"type": "prometheus"}, "definition": "query_result(count(up{job=\"scylla-db-sm\"}) by (dc))", "hide": 2, "includeAll": true, "multi": true, "name": "count_dc", "options": [], "query": {"query": "query_result(count(up{job=\"scylla-db-sm\"}) by (dc))", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "/(?<dc>\\{dc=\"[^\"]+\".* \\d+) .*/", "sort": 1, "type": "query"}, {"current": {"text": "5.4", "value": "5.4"}, "hide": 2, "includeAll": false, "name": "scylla_version", "options": [{"selected": true, "text": "5.4", "value": "5.4"}], "query": "5.4", "type": "custom"}, {"current": {"text": "4.8.0", "value": "4.8.0"}, "hide": 2, "includeAll": false, "name": "monitoring_version", "options": [{"selected": true, "text": "4.8.0", "value": "4.8.0"}], "query": "4.8.0", "type": "custom"}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "utc", "title": "Scylla Overview", "uid": "scylla-overview-5-4", "version": 1, "weekStart": ""}