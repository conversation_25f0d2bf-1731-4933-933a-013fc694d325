{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 5013, "links": [], "panels": [{"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 12, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum (settlement_message_with_transaction_count_gauge{job=\"$job\", namespace=\"$namespace\", pod=~\"ews-betting-processor.+\"}) by (job)", "interval": "", "legendFormat": "settlement avg  count  with transactions{{pod}}", "refId": "A"}], "title": "Total count  settlement_message_with_transaction_count_gauge", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 14, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "settlement_avg_message_with_transaction_processing_time_gauge{job=\"$job\", namespace=\"$namespace\", pod=~\"ews-betting-processor.+\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "settlement_avg_message_with_transaction_processing_time_gauge", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Settlement over avg message count"}, "properties": [{"id": "color", "value": {"fixedColor": "purple", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Settlement over avg message count"}, "properties": [{"id": "color", "value": {"fixedColor": "purple", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 24, "x": 0, "y": 8}, "id": 8, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "lastNotNull", "max", "min", "sum"], "displayMode": "table", "placement": "right", "showLegend": true, "width": 480}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(settlement_over_avg_message_count_gauge{job=\"$job\",namespace=\"$namespace\"}) by (job)", "interval": "", "legendFormat": "Settlement over avg message count", "refId": "A"}], "title": "Total count settlement_over_avg_message_count_gauge", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "settlement under avg  count "}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "settlement under avg  count "}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}]}, "gridPos": {"h": 4, "w": 24, "x": 0, "y": 13}, "id": 9, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "lastNotNull", "max", "min", "sum"], "displayMode": "table", "placement": "right", "showLegend": true, "width": 480}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum (settlement_under_avg_message_count_gauge{job=\"$job\", namespace=\"$namespace\", pod=~\"ews-betting-processor.+\"}) by (job)", "interval": "", "legendFormat": "settlement under avg  count {{pod}}", "refId": "A"}], "title": "Total count  settlement_under_avg_message_count_gauge", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 17}, "id": 2, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "settlement_avg_message_processing_time_gauge{job=\"$job\", namespace=\"$namespace\", pod=~\"ews-betting-processor.+\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "settlement_avg_message_processing_time_gauge", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 17}, "id": 4, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "placement_avg_message_processing_time_gauge{job=\"$job\", namespace=\"$namespace\", pod=~\"ews-betting-processor.+\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "placement_avg_message_processing_time_gauge", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 26}, "id": 26, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "refId": "A"}], "title": "Panel Title", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 0, "y": 33}, "id": 22, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "service_time_settlement_to_processor_milliseconds_max", "interval": "", "legendFormat": "", "refId": "A"}], "title": "[Service] Settlement to Processor", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 12, "y": 33}, "id": 24, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "waiting_settlement_to_processor_milliseconds_max", "interval": "", "legendFormat": "", "refId": "A"}], "title": "[Waiting] Settlement to Processor Requests", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 44}, "id": 23, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "service_processor_stages_to_async_processing_milliseconds_max", "interval": "", "legendFormat": "", "refId": "A"}], "title": "[Service] Processor Stages", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 44}, "id": 25, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "waiting_processor_stages_to_async_processing_milliseconds_max", "interval": "", "legendFormat": "", "refId": "A"}], "title": "[Waiting] Processor Stages", "type": "timeseries"}], "preload": false, "refresh": "", "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "", "value": ""}, "label": "Feed Type", "name": "feed_type", "options": [{"selected": true, "text": "Premium", "value": ""}, {"selected": false, "text": "Standard", "value": "standard-"}], "query": "Premium :   ,Standard : standard-", "type": "custom"}, {"current": {"text": "ews-betting-processor", "value": "ews-betting-processor"}, "datasource": {"type": "prometheus"}, "definition": "label_values(jvm_classes_loaded_classes{}, job)", "includeAll": false, "label": "Job", "name": "job", "options": [], "query": {"query": "label_values(jvm_classes_loaded_classes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/^${feed_type}ews-betting-processor.*/", "type": "query"}, {"current": {"text": "ews-dev", "value": "ews-dev"}, "datasource": {"type": "prometheus"}, "definition": "label_values(jvm_classes_loaded_classes{job=\"$job\"}, namespace)", "includeAll": false, "label": "Namespace", "name": "namespace", "options": [], "query": {"query": "label_values(jvm_classes_loaded_classes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "172.17.45.192:10013", "value": "172.17.45.192:10013"}, "datasource": {"type": "prometheus"}, "definition": "label_values(jvm_classes_loaded_classes{job=\"$job\", namespace=\"$namespace\"},instance)", "includeAll": false, "label": "Instance", "name": "instance", "options": [], "query": {"query": "label_values(jvm_classes_loaded_classes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Betting processor - Custom metrics", "uid": "r9Vksdfsdgg3523eywf", "version": 1, "weekStart": ""}