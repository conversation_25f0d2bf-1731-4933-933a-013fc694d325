{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 4792, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 22, "panels": [], "title": "General audit telemetry", "type": "row"}, {"datasource": {"type": "prometheus"}, "description": "This is general metric to trace endpoints calls in order to show API activities", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 1}, "id": 2, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (service_name) (increase(audit_logs_api_different_endpoints_hit_count [5m])) > 0", "interval": "", "legendFormat": "Service: {{service_name}}", "range": true, "refId": "A"}], "title": "API Hits Counter", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Audit Collector errors on validations and kafka. The sum is made by product and front-end location. To see which product or front-end page is faling.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 1}, "id": 24, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (product) (increase(audit_logs_validation_errors_counter[5m])) > 0", "interval": "", "legendFormat": "Validation failed - {{product}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (product ) (increase(audit_kafka_errors_produced_counter[5m])) > 0", "hide": false, "interval": "", "legendFormat": "Kafka failure - {{product}} ", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (product) (increase(audits_kafka_successful_counter[5m])) > 0", "hide": false, "interval": "", "legendFormat": "Produced - {{product}}", "range": true, "refId": "C"}], "title": "Collector - Validation/Kafka Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 8}, "id": 14, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (service,type) (increase(audit_logs_failed_store_counter[5m])) > 0", "interval": "", "legendFormat": "\"{{service}}\" failed with - {{type}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(audit_logs_store_counter[5m]) - rate(audit_logs_produced_store_counter[5m]) > 0", "hide": false, "interval": "", "legendFormat": "Stored for: \"{{service}}\"", "range": true, "refId": "C"}], "title": "Producer Telemetry - Stored/Failed", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Keeps track of successfully produced audit logs summed by product and service. This is activity tracker.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 8}, "id": 36, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (service, product) (increase(audit_logs_produced_store_counter[5m])) > 0", "interval": "", "legendFormat": "{{product}}  -  {{service}}", "range": true, "refId": "A"}], "title": "Produced Logs by product/service", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 14}, "id": 34, "panels": [], "title": "Errors", "type": "row"}, {"datasource": {"type": "prometheus"}, "description": "This is total counter for errors split only by  service. Just to trace whether there are some errors in the app", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 15}, "id": 32, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (service_name) (increase(audit_logs_api_total_error_count[5m])) > 0", "interval": "", "legendFormat": "{{label_name}}", "range": true, "refId": "A"}], "title": "Errors Total Count", "type": "timeseries"}], "preload": false, "refresh": false, "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "ews-audit-collector", "value": "ews-audit-collector"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{}, job)", "includeAll": false, "label": "Job", "name": "job", "options": [], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "ews-dev", "value": "ews-dev"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "includeAll": false, "label": "Namespace", "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "172.17.51.18:8080", "value": "172.17.51.18:8080"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "includeAll": false, "label": "Instance", "name": "instance", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"current": {"text": "ews-audit-collector-5b77cd658f-htz4k", "value": "ews-audit-collector-5b77cd658f-htz4k"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "hide": 2, "includeAll": false, "label": "Pod", "name": "pod", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "ews-audit-collector-5b77cd658f-htz4k", "value": "ews-audit-collector-5b77cd658f-htz4k"}, "datasource": {"type": "prometheus"}, "definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "includeAll": false, "label": "Podname", "name": "podname", "options": [], "query": {"query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "ews-log-audit", "uid": "d3f2a1c6789e4b2e5f71127", "version": 1, "weekStart": ""}