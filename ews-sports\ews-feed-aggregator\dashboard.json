{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 4929, "links": [], "panels": [{"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 50, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "avg(rate(execution_duration_seconds_sum{job=~\"$job\", namespace=~\"$namespace\"}[5m])/rate(execution_duration_seconds_count{job=~\"$job\", namespace=~\"$namespace\"}[5m])) by (action, controller)", "interval": "", "legendFormat": " {{controller}}.{{action}}", "refId": "A"}], "title": "Execution Durations", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 52, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(block_size{job=~\"$job\", namespace=~\"$namespace\"}) by (block)", "interval": "", "legendFormat": "{{block}}", "refId": "A"}], "title": "Block Size", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 54, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "avg(rate(feedhub_message_processing_time_sum{job=~\"$job\", namespace=~\"$namespace\"}[5m])/rate(feedhub_message_processing_time_count{job=~\"$job\", namespace=~\"$namespace\"}[5m])) by (feed)", "interval": "", "legendFormat": "{{feed}}", "refId": "A"}], "title": "Execution Durations", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 56, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "avg(rate(feedhub_provider_kafka_delay_seconds_sum{job=~\"$job\", namespace=~\"$namespace\"}[5m])/rate(feedhub_provider_kafka_delay_seconds_count{job=~\"$job\", namespace=~\"$namespace\"}[5m])) by (feed)\r", "interval": "", "legendFormat": "Time in Kafka in Seconds - {{feed}}", "refId": "A"}], "title": "Service Time", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 17, "x": 0, "y": 16}, "id": 58, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(kafka_consumergroup_lag{consumergroup=~\"BetRadarConsumer_DEV\",topic=~\"feed-updates-betradar\"}) by (consumergroup)", "interval": "", "legendFormat": "{{consumergroup}}", "refId": "A"}], "title": "Kafka Lag", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 7, "x": 17, "y": 16}, "id": 60, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(kafka_errors{job=\"$job\", namespace=\"$namespace\"}[1m]) * 60", "interval": "", "legendFormat": "{{action}}", "refId": "A"}], "title": "Kafka Events", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "id": 62, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(pipeline_errors{job=~\"$job\", namespace=~\"$namespace\"}[1m]) * 60", "interval": "", "legendFormat": "{{error}}", "refId": "A"}], "title": "Pipeline Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "id": 64, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(producer_errors{job=~\"$job\", namespace=~\"$namespace\"}[1m]) * 60", "interval": "", "legendFormat": "{{error}}", "refId": "A"}], "title": "Producer Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "id": 66, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(redis_errors{job=~\"$job\", namespace=~\"$namespace\"}[1m]) * 60", "interval": "", "legendFormat": "{{error}}", "refId": "A"}], "title": "<PERSON><PERSON>", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "id": 68, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "rate(kafka_errors{job=~\"$job\", namespace=~\"$namespace\"}[1m]) * 60", "interval": "", "legendFormat": "{{action}}", "refId": "A"}], "title": "<PERSON><PERSON><PERSON>", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 40}, "id": 42, "panels": [{"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 1}, "id": 34, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(execution_failed_total{job=\"$job\",namespace=\"$namespace\"}[1m])) by (controller,action,pod)", "interval": "", "legendFormat": "{{controller}}.{{action}} ({{pod}})", "refId": "A"}], "title": "ERRORS", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 1}, "id": 48, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(aspnetcore_healthcheck_status{job=\"$job\",namespace=\"$namespace\"}) by (pod,name)", "interval": "", "legendFormat": "{{name}} {{pod}}", "refId": "A"}], "title": "Healthchecks", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 8}, "id": 36, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(execution_duration_seconds_sum{job=\"$job\",namespace=\"$namespace\"}[5m])/rate(execution_duration_seconds_count{job=\"$job\",namespace=\"$namespace\"}[5m])) by (controller,action,job)", "interval": "", "legendFormat": "{{job}}.{{controller}}.{{action}}", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "sum(rate(http_request_duration_seconds_sum{job=\"$job\",namespace=\"$namespace\", action != \"\"}[5m])) by (action,code,controller) / sum(rate(http_request_duration_seconds_count{job=\"$job\",namespace=\"$namespace\", action != \"\"}[5m])) by (action,code,controller)", "hide": false, "interval": "", "legendFormat": "{{controller}}.{{action}} [{{code}}]", "refId": "C"}], "title": "Execution Durations in Seconds", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 8}, "id": 38, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(execution_duration_seconds_count{job=\"$job\",namespace=\"$namespace\"}[$__rate_interval])) by (controller,action,job)", "interval": "", "legendFormat": "{{job}}.{{controller}}.{{action}}", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "sum(rate(http_request_duration_seconds_count{job=\"$job\",namespace=\"$namespace\", action != \"\"}[$__rate_interval])) by (action,code,controller)", "hide": false, "interval": "", "legendFormat": "{{action}} [{{code}}] {{controller}}", "refId": "C"}], "title": "Execution Rates", "type": "timeseries"}], "title": "General", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 41}, "id": 22, "panels": [{"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 2}, "id": 2, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(process_cpu_seconds_total{job=\"$job\",namespace=\"$namespace\"}[5m])) by (pod)", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "CPU Cores Used", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 2}, "id": 24, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_private_memory_bytes{job=\"$job\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Private Memory Bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 2}, "id": 28, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_num_threads{job=\"$job\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Process Threads", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 2}, "id": 32, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_working_set_bytes{job=\"$job\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Working Set Bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 9}, "id": 26, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_open_handles{job=\"$job\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Open Handles", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 9}, "id": 30, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_start_time_seconds{job=\"$job\",namespace=\"$namespace\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Start Time Seconds", "type": "timeseries"}], "title": "Resources", "type": "row"}], "preload": false, "refresh": "5s", "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "", "value": ""}, "label": "Feed Type", "name": "feed_type", "options": [{"selected": true, "text": "Premium", "value": ""}, {"selected": false, "text": "Standard", "value": "standard-"}], "query": "Premium :   ,Standard : standard-", "type": "custom"}, {"allValue": "all", "current": {"text": "ews-feed-aggregator", "value": "ews-feed-aggregator"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{}, job)", "includeAll": false, "label": "Job", "name": "job", "options": [], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/^${feed_type}ews-feed-aggregator/", "type": "query"}, {"current": {"text": "ews-dev", "value": "ews-dev"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "includeAll": false, "label": "Namespace", "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"current": {"text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "hide": 2, "includeAll": true, "label": "Instance", "multi": true, "name": "instance", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"current": {"text": "ews-feed-aggregator-5f784869cb-d2cgm", "value": "ews-feed-aggregator-5f784869cb-d2cgm"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "hide": 2, "includeAll": false, "label": "Pod", "name": "pod", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "ews-feed-aggregator-5f784869cb-d2cgm", "value": "ews-feed-aggregator-5f784869cb-d2cgm"}, "datasource": {"type": "prometheus"}, "definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "hide": 2, "includeAll": false, "label": "Podname", "name": "podname", "options": [], "query": {"query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Feed aggregator", "uid": "HYvnbhutp", "version": 1, "weekStart": ""}