{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 547, "iteration": 1711095875372, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 40, "panels": [], "title": "Stats", "type": "row"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 1}, "id": 38, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "right"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum by (<PERSON>, Controller) (rate(incoming_request_duration_sum{job=\"ews-authorisation-api\", namespace=\"$namespace\"}[5m])/rate(incoming_request_duration_count{job=\"ews-authorisation-api\", namespace=\"$namespace\"}[5m]))", "interval": "", "legendFormat": "{{Method}} {{Controller}}", "refId": "A"}], "title": "Incoming Request Duration", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "NewGames"}, "properties": [{"id": "color", "value": {"fixedColor": "rgba(50, 116, 217, 0.5)", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "RecommendedGames"}, "properties": [{"id": "color", "value": {"fixedColor": "rgba(255, 120, 10, 0.5)", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "SimilarGames"}, "properties": [{"id": "color", "value": {"fixedColor": "rgba(242, 204, 12, 0.5)", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 1}, "id": 42, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "right"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.4.2", "targets": [{"expr": "sum by (Method, Target) (rate(outgoing_request_duration_sum{job=\"ews-authorisation-api\", namespace=\"$namespace\"}[5m])/rate(outgoing_request_duration_count{job=\"ews-authorisation-api\", namespace=\"$namespace\"}[5m]))", "interval": "", "legendFormat": "{{Method}} {{Target}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Outgoing Request Duration", "type": "timeseries"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 30, "panels": [], "title": "Authorization Api", "type": "row"}, {"aliasColors": {"Because You Played": "rgba(31, 96, 196, 0.8)", "I Feel Lucky": "rgba(224, 180, 0, 0.8)", "New Games": "rgba(55, 135, 45, 0.8)", "Recommended For You": "rgba(143, 59, 184, 0.8)", "errors": "rgba(196, 22, 42, 0.8)"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 9}, "hiddenSeries": false, "id": 16, "legend": {"avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (type, BU) (increase(count_login{job=\"ews-authorisation-api\", namespace=\"$namespace\"}[5m]))", "interval": "", "legendFormat": "{{type}} {{BU}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Count <PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:661", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:662", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Because You Played": "rgba(31, 96, 196, 0.8)", "I Feel Lucky": "rgba(224, 180, 0, 0.8)", "New Games": "rgba(55, 135, 45, 0.8)", "Recommended For You": "rgba(143, 59, 184, 0.8)", "Trending Now": "rgba(250, 100, 0, 0.8)"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 6, "x": 12, "y": 9}, "hiddenSeries": false, "id": 18, "legend": {"avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (source) (increase(count_login_sources{job=\"ews-authorisation-api\", namespace=\"$namespace\"}[5m]))", "interval": "", "legendFormat": "{{source}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:748", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:749", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"IBBG": "rgba(55, 135, 45, 0.8)", "WBBG": "rgba(86, 166, 75, 0.8)", "WBRO": "rgba(150, 217, 141, 0.8)", "WBTZ": "rgba(200, 242, 194, 0.8)"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 6, "w": 6, "x": 18, "y": 9}, "hiddenSeries": false, "id": 20, "legend": {"avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (<PERSON><PERSON><PERSON>, BU) (increase(count_retries{job=\"ews-authorisation-api\", namespace=\"$namespace\"}[5m]))", "interval": "", "legendFormat": "{{taskName}} {{BU}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Retries", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:997", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:998", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 15}, "id": 34, "panels": [], "title": "Error", "type": "row"}, {"aliasColors": {"aaa": "rgba(255, 166, 176, 0.8)", "ews-recommendations-api": "rgba(196, 22, 42, 0.8)", "ews-recommendations-internal-data-populator": "light-red", "ews-recommendations-player-segments-updater": "rgba(224, 47, 68, 0.8)", "ews-recommendations-vaix-data-populator": "rgba(255, 166, 176, 0.8)", "ews-recommendations-vaix-interactions-populator": "rgba(237, 71, 90, 0.8)"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"color": {}, "custom": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 24, "x": 0, "y": 16}, "hiddenSeries": false, "id": 22, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(count_errors{job=\"ews-authorisation-api\", namespace=\"$namespace\"}[5m])", "hide": false, "interval": "", "legendFormat": "errors", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Errors", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1651", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1652", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "5s", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "ews-authorisation-api", "value": "ews-authorisation-api"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{},job)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Job", "multi": false, "name": "job", "options": [{"selected": true, "text": "ews-authorisation-api", "value": "ews-authorisation-api"}], "query": {"query": "label_values(process_private_memory_bytes{},job)", "refId": "StandardVariableQuery"}, "refresh": 0, "regex": "/ews-authorisation-api.*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-sof-prod", "value": "ews-sof-prod"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Namespace", "multi": false, "name": "namespace", "options": [{"selected": true, "text": "ews-sof-prod", "value": "ews-sof-prod"}], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Authorisation Api", "uid": "ger<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": 4}