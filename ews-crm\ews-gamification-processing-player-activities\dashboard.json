{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 4579, "links": [], "panels": [{"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 23, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"editorMode": "code", "expr": "sum by (BusinessUnit, pod) (\r\n  increase(given_bonus_award_counter{namespace=\"$namespace\", pod=~\".*\"}[1h])\r\n  * on (namespace, pod)\r\n  group_left(workload, workload_type)\r\n  namespace_workload_pod:kube_pod_owner:relabel{\r\n    namespace=\"$namespace\",\r\n    workload=~\"$stream_events_processor_name\",\r\n    workload_type=~\".*\"\r\n  }\r\n)\r\n", "legendFormat": "{{pod}} - {{BusinessUnit}}", "range": true, "refId": "A"}], "title": "Given Bonus Awards Count", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 25, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"editorMode": "code", "expr": "sum by (BusinessUnit, pod) (\r\n  increase(given_player_coins_counter{namespace=\"$namespace\", pod=~\".*\"}[1h])\r\n  * on (namespace, pod)\r\n  group_left(workload, workload_type)\r\n  namespace_workload_pod:kube_pod_owner:relabel{\r\n    namespace=\"$namespace\",\r\n    workload=~\"$stream_events_processor_name\",\r\n    workload_type=~\".*\"\r\n  }\r\n)", "legendFormat": "{{pod}} - {{BusinessUnit}}", "range": true, "refId": "A"}], "title": "Given Coins Awards Count", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 5, "y": 8}, "id": 24, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"editorMode": "code", "expr": "sum by (BusinessUnit, pod) (\r\n  increase(created_player_mission_Counter{namespace=\"$namespace\", pod=~\".*\"}[1h])\r\n  * on (namespace, pod)\r\n  group_left(workload, workload_type)\r\n  namespace_workload_pod:kube_pod_owner:relabel{\r\n    namespace=\"$namespace\",\r\n    workload=~\"$stream_events_processor_name\",\r\n    workload_type=~\".*\"\r\n  }\r\n)", "legendFormat": "{{pod}} - {{BusinessUnit}}", "range": true, "refId": "A"}], "title": "Created Player Missions Count", "type": "timeseries"}], "preload": false, "refresh": "", "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "ews-dev", "value": "ews-dev"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "includeAll": false, "label": "Namespace", "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "ews-gamification-stream-events-processor", "value": "ews-gamification-stream-events-processor"}, "datasource": {"type": "prometheus"}, "definition": "label_values(namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\"},workload)", "hide": 2, "includeAll": false, "label": "Stream Events Processor Name", "name": "stream_events_processor_name", "options": [], "query": {"qryType": 1, "query": "label_values(namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\"},workload)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "/ews-gamification-stream-events-processor.*/", "type": "query"}]}, "time": {"from": "now-2d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Gamification Player Activities", "uid": "U9f6vhebvvibh56sghty", "version": 1, "weekStart": ""}